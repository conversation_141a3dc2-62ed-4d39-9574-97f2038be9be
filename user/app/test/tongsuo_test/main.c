/*
 * Copyright (C) 2024 The Tongsuo Project Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://github.com/Tongsuo-Project/Tongsuo/blob/master/LICENSE.txt
 */

#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// Test both BoringSSL and Tongsuo headers can be included
#include <openssl/crypto.h>  // BoringSSL

int main(void)
{
    printf("Testing Tongsuo and BoringSSL coexistence...\n");

    // Test BoringSSL functionality
    printf("BoringSSL version: %s\n", OpenSSL_version(OPENSSL_VERSION));

    printf("Tongsuo and BoringSSL coexistence test completed successfully!\n");

    return 0;
}
