# Copyright (c) 2024, Tongsuo Project.
#
# Licensed under the Apache License, Version 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://github.com/Tongsuo-Project/Tongsuo/blob/master/LICENSE.txt

# This file is auto-generated for Trusty integration. Do not edit manually.

crypto_sources := \
  crypto/aes/aes_cfb.c\
  crypto/aes/aes_ecb.c\
  crypto/aes/aes_ige.c\
  crypto/aes/aes_misc.c\
  crypto/aes/aes_ofb.c\
  crypto/aes/aes_wrap.c\
  crypto/asn1/n_pkey.c\
  crypto/asn1/x_delegated_credential.c\
  crypto/asn1/x_long.c\
  crypto/asn1_dsa.c\
  crypto/bn/bn_add.c\
  crypto/bn/bn_ctx.c\
  crypto/bn/bn_depr.c\
  crypto/bn/bn_div.c\
  crypto/bn/bn_err.c\
  crypto/bn/bn_exp.c\
  crypto/bn/bn_lib.c\
  crypto/bn/bn_meth.c\
  crypto/bn/bn_mul.c\
  crypto/bn/bn_print.c\
  crypto/bn/bn_sm2.c\
  crypto/bn/bn_srp.c\
  crypto/bn/bn_x931p.c\
  crypto/bsearch.c\
  crypto/buffer/buf_err.c\
  crypto/buffer/buffer.c\
  crypto/cmac/cmac.c\
  crypto/cmp/cmp_asn.c\
  crypto/cmp/cmp_ctx.c\
  crypto/cmp/cmp_err.c\
  crypto/cmp/cmp_util.c\
  crypto/context.c\
  crypto/core_algorithm.c\
  crypto/core_fetch.c\
  crypto/core_namemap.c\
  crypto/cpt_err.c\
  crypto/cpuid.c\
  crypto/crmf/crmf_asn.c\
  crypto/crmf/crmf_err.c\
  crypto/crmf/crmf_lib.c\
  crypto/crmf/crmf_pbm.c\
  crypto/cryptlib.c\
  crypto/ct/ct_b64.c\
  crypto/ct/ct_err.c\
  crypto/ct/ct_log.c\
  crypto/ct/ct_oct.c\
  crypto/ct/ct_policy.c\
  crypto/ctype.c\
  crypto/cversion.c\
  crypto/der_writer.c\
  crypto/des/ecb3_enc.c\
  crypto/des/set_key.c\
  crypto/dh/dh_backend.c\
  crypto/dh/dh_check.c\
  crypto/dh/dh_depr.c\
  crypto/dh/dh_gen.c\
  crypto/dh/dh_group_params.c\
  crypto/dh/dh_key.c\
  crypto/dh/dh_lib.c\
  crypto/dllmain.c\
  crypto/dsa/dsa_check.c\
  crypto/dsa/dsa_depr.c\
  crypto/dsa/dsa_lib.c\
  crypto/dsa/dsa_ossl.c\
  crypto/dsa/dsa_sign.c\
  crypto/dsa/dsa_vrf.c\
  crypto/ebcdic.c\
  crypto/ec/ec_ameth.c\
  crypto/ec/ec_cvt.c\
  crypto/ec/ec_elgamal_crypt.c\
  crypto/ec/ec_elgamal_dlog.c\
  crypto/ec/ec_elgamal_encode.c\
  crypto/ec/ec_lib.c\
  crypto/ec/ec_mult.c\
  crypto/ec/ec_pmeth.c\
  crypto/ec/ecp_mont.c\
  crypto/ec/ecp_nist.c\
  crypto/ec/ecp_nistp224.c\
  crypto/ec/ecp_nistp256.c\
  crypto/ec/ecp_nistp521.c\
  crypto/ec/ecp_nistputil.c\
  crypto/ec/ecp_sm2p256.c\
  crypto/ec/ecp_smpl.c\
  crypto/ec/ecx_meth.c\
  crypto/eia3/eia3.c\
  crypto/encode_decode/decoder_err.c\
  crypto/encode_decode/decoder_lib.c\
  crypto/encode_decode/decoder_meth.c\
  crypto/encode_decode/decoder_pkey.c\
  crypto/encode_decode/encoder_err.c\
  crypto/encode_decode/encoder_lib.c\
  crypto/encode_decode/encoder_meth.c\
  crypto/encode_decode/encoder_pkey.c\
  crypto/engine/eng_ctrl.c\
  crypto/engine/eng_err.c\
  crypto/engine/eng_init.c\
  crypto/engine/eng_lib.c\
  crypto/engine/eng_list.c\
  crypto/engine/tb_bnmeth.c\
  crypto/ess/ess_asn1.c\
  crypto/ess/ess_err.c\
  crypto/ess/ess_lib.c\
  crypto/evp/dh_ctrl.c\
  crypto/evp/digest.c\
  crypto/evp/dsa_ctrl.c\
  crypto/evp/e_old.c\
  crypto/evp/ec_ctrl.c\
  crypto/evp/evp_enc.c\
  crypto/evp/evp_fetch.c\
  crypto/evp/evp_lib.c\
  crypto/evp/evp_utils.c\
  crypto/evp/legacy_md5.c\
  crypto/evp/legacy_md5_sha1.c\
  crypto/evp/p_dec.c\
  crypto/evp/p_enc.c\
  crypto/evp/p_open.c\
  crypto/ex_data.c\
  crypto/ffc/ffc_key_generate.c\
  crypto/ffc/ffc_params.c\
  crypto/ffc/ffc_params_generate.c\
  crypto/getenv.c\
  crypto/hmac/hmac.c\
  crypto/http/http_client.c\
  crypto/http/http_err.c\
  crypto/http/http_lib.c\
  crypto/info.c\
  crypto/init.c\
  crypto/initthread.c\
  crypto/kdf/kdf_err.c\
  crypto/md5/md5_dgst.c\
  crypto/md5/md5_one.c\
  crypto/md5/md5_sha1.c\
  crypto/mem.c\
  crypto/mem_clr.c\
  crypto/modes/cbc128.c\
  crypto/modes/ccm128.c\
  crypto/modes/cfb128.c\
  crypto/modes/ctr128.c\
  crypto/modes/gcm128.c\
  crypto/modes/ofb128.c\
  crypto/modes/xts128.c\
  crypto/o_dir.c\
  crypto/o_fopen.c\
  crypto/o_init.c\
  crypto/o_str.c\
  crypto/o_time.c\
  crypto/packet.c\
  crypto/paillier/paillier_asn1.c\
  crypto/paillier/paillier_crypt.c\
  crypto/paillier/paillier_key.c\
  crypto/param_build.c\
  crypto/param_build_set.c\
  crypto/params.c\
  crypto/params_dup.c\
  crypto/params_from_text.c\
  crypto/passphrase.c\
  crypto/poly1305/poly1305.c\
  crypto/property/defn_cache.c\
  crypto/property/property.c\
  crypto/property/property_err.c\
  crypto/property/property_parse.c\
  crypto/property/property_query.c\
  crypto/property/property_string.c\
  crypto/provider.c\
  crypto/provider_child.c\
  crypto/provider_conf.c\
  crypto/provider_core.c\
  crypto/provider_predefined.c\
  crypto/punycode.c\
  crypto/rand/rand_lib.c\
  crypto/rsa/rsa_depr.c\
  crypto/rsa/rsa_gen.c\
  crypto/rsa/rsa_lib.c\
  crypto/rsa/rsa_ossl.c\
  crypto/rsa/rsa_pk1.c\
  crypto/rsa/rsa_sign.c\
  crypto/rsa/rsa_x931g.c\
  crypto/self_test_core.c\
  crypto/sha/sha1_one.c\
  crypto/sha/sha1dgst.c\
  crypto/sha/sha256.c\
  crypto/sha/sha3.c\
  crypto/sha/sha512.c\
  crypto/sm2/sm2_threshold.c\
  crypto/sm3/legacy_sm3.c\
  crypto/sm3/sm3.c\
  crypto/sm4/sm4.c\
  crypto/sparse_array.c\
  crypto/srp/srp_lib.c\
  crypto/srp/srp_vfy.c\
  crypto/stack/stack.c\
  crypto/store/store_init.c\
  crypto/store/store_register.c\
  crypto/threads_lib.c\
  crypto/threads_none.c\
  crypto/trace.c\
  crypto/txt_db/txt_db.c\
  crypto/uid.c\
  crypto/x509/x509type.c\
  crypto/zkp/bulletproofs/bulletproofs.c\
  crypto/zkp/common/zkp_transcript.c\
  crypto/zkp/common/zkp_util.c\
  crypto/zkp/gadget/zkp_range_proof.c\
  crypto/zkp/nizk/nizk.c\
  crypto/zuc/zuc.c

crypto_sources_asm :=

