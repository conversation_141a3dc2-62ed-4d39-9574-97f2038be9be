# Generated with generate_ssl_tests.pl

num_tests = 22

test-0 = 0-ECC SM2 client auth request
test-1 = 1-ECC SM2 client auth require fail
test-2 = 2-ECC SM2 client auth require
test-3 = 3-ECC SM2 client auth non empty names
test-4 = 4-ECC SM2 client auth noroot
test-5 = 5-ECC SM2 client encryption certificate expired
test-6 = 6-ECC SM2 client sign certificate expired
test-7 = 7-ECC SM2 client certificates expired
test-8 = 8-ECDHE SM2 client auth
test-9 = 9-ECDHE SM2 client auth non empty names
test-10 = 10-ECDHE SM2 client auth noroot
test-11 = 11-ECDHE SM2 client encryption certificate expired
test-12 = 12-ECDHE SM2 client sign certificate expired
test-13 = 13-ECDHE SM2 client certificates expired
test-14 = 14-RSA client auth request
test-15 = 15-RSA client auth require fail
test-16 = 16-RSA client auth require
test-17 = 17-RSA client auth non empty names
test-18 = 18-RSA client auth noroot
test-19 = 19-RSA client encryption certificate expired
test-20 = 20-RSA client sign certificate expired
test-21 = 21-RSA client certificates expired
# ===========================================================

[0-ECC SM2 client auth request]
ssl_conf = 0-ECC SM2 client auth request-ssl

[0-ECC SM2 client auth request-ssl]
server = 0-ECC SM2 client auth request-server
client = 0-ECC SM2 client auth request-client

[0-ECC SM2 client auth request-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Request

[0-ECC SM2 client auth request-client]
CipherString = ECC-SM2-SM4-GCM-SM3
Enable_ntls = on
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-0]
ExpectedCipher = ECC-SM2-SM4-GCM-SM3
ExpectedProtocol = NTLS
ExpectedResult = Success
Method = NTLS


# ===========================================================

[1-ECC SM2 client auth require fail]
ssl_conf = 1-ECC SM2 client auth require fail-ssl

[1-ECC SM2 client auth require fail-ssl]
server = 1-ECC SM2 client auth require fail-server
client = 1-ECC SM2 client auth require fail-client

[1-ECC SM2 client auth require fail-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Require

[1-ECC SM2 client auth require fail-client]
CipherString = ECC-SM2-SM4-GCM-SM3
Enable_ntls = on
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-1]
ExpectedResult = ServerFail
ExpectedServerAlert = HandshakeFailure
Method = NTLS


# ===========================================================

[2-ECC SM2 client auth require]
ssl_conf = 2-ECC SM2 client auth require-ssl

[2-ECC SM2 client auth require-ssl]
server = 2-ECC SM2 client auth require-server
client = 2-ECC SM2 client auth require-client

[2-ECC SM2 client auth require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Require

[2-ECC SM2 client auth require-client]
CipherString = ECC-SM2-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-2]
ExpectedCipher = ECC-SM2-SM4-GCM-SM3
ExpectedClientCANames = empty
ExpectedClientCertType = SM2
ExpectedProtocol = NTLS
ExpectedResult = Success
Method = NTLS


# ===========================================================

[3-ECC SM2 client auth non empty names]
ssl_conf = 3-ECC SM2 client auth non empty names-ssl

[3-ECC SM2 client auth non empty names-ssl]
server = 3-ECC SM2 client auth non empty names-server
client = 3-ECC SM2 client auth non empty names-client

[3-ECC SM2 client auth non empty names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Require

[3-ECC SM2 client auth non empty names-client]
CipherString = ECC-SM2-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-3]
ExpectedCipher = ECC-SM2-SM4-GCM-SM3
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
ExpectedClientCertType = SM2
ExpectedProtocol = NTLS
ExpectedResult = Success
Method = NTLS


# ===========================================================

[4-ECC SM2 client auth noroot]
ssl_conf = 4-ECC SM2 client auth noroot-ssl

[4-ECC SM2 client auth noroot-ssl]
server = 4-ECC SM2 client auth noroot-server
client = 4-ECC SM2 client auth noroot-client

[4-ECC SM2 client auth noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key
VerifyMode = Require

[4-ECC SM2 client auth noroot-client]
CipherString = ECC-SM2-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-4]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA
Method = NTLS


# ===========================================================

[5-ECC SM2 client encryption certificate expired]
ssl_conf = 5-ECC SM2 client encryption certificate expired-ssl

[5-ECC SM2 client encryption certificate expired-ssl]
server = 5-ECC SM2 client encryption certificate expired-server
client = 5-ECC SM2 client encryption certificate expired-client

[5-ECC SM2 client encryption certificate expired-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Require

[5-ECC SM2 client encryption certificate expired-client]
CipherString = ECC-SM2-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_enc_expire.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-5]
ExpectedResult = ServerFail
ExpectedServerAlert = CertificateExpired
Method = NTLS


# ===========================================================

[6-ECC SM2 client sign certificate expired]
ssl_conf = 6-ECC SM2 client sign certificate expired-ssl

[6-ECC SM2 client sign certificate expired-ssl]
server = 6-ECC SM2 client sign certificate expired-server
client = 6-ECC SM2 client sign certificate expired-client

[6-ECC SM2 client sign certificate expired-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Require

[6-ECC SM2 client sign certificate expired-client]
CipherString = ECC-SM2-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_sign_expire.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-6]
ExpectedResult = ServerFail
ExpectedServerAlert = CertificateExpired
Method = NTLS


# ===========================================================

[7-ECC SM2 client certificates expired]
ssl_conf = 7-ECC SM2 client certificates expired-ssl

[7-ECC SM2 client certificates expired-ssl]
server = 7-ECC SM2 client certificates expired-server
client = 7-ECC SM2 client certificates expired-client

[7-ECC SM2 client certificates expired-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Require

[7-ECC SM2 client certificates expired-client]
CipherString = ECC-SM2-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_enc_expire.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_sign_expire.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-7]
ExpectedResult = ServerFail
ExpectedServerAlert = CertificateExpired
Method = NTLS


# ===========================================================

[8-ECDHE SM2 client auth]
ssl_conf = 8-ECDHE SM2 client auth-ssl

[8-ECDHE SM2 client auth-ssl]
server = 8-ECDHE SM2 client auth-server
client = 8-ECDHE SM2 client auth-client

[8-ECDHE SM2 client auth-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt

[8-ECDHE SM2 client auth-client]
CipherString = ECDHE-SM2-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-8]
ExpectedCipher = ECDHE-SM2-SM4-GCM-SM3
ExpectedClientCANames = empty
ExpectedClientCertType = SM2
ExpectedProtocol = NTLS
ExpectedResult = Success
Method = NTLS


# ===========================================================

[9-ECDHE SM2 client auth non empty names]
ssl_conf = 9-ECDHE SM2 client auth non empty names-ssl

[9-ECDHE SM2 client auth non empty names-ssl]
server = 9-ECDHE SM2 client auth non empty names-server
client = 9-ECDHE SM2 client auth non empty names-client

[9-ECDHE SM2 client auth non empty names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt

[9-ECDHE SM2 client auth non empty names-client]
CipherString = ECDHE-SM2-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-9]
ExpectedCipher = ECDHE-SM2-SM4-GCM-SM3
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
ExpectedClientCertType = SM2
ExpectedProtocol = NTLS
ExpectedResult = Success
Method = NTLS


# ===========================================================

[10-ECDHE SM2 client auth noroot]
ssl_conf = 10-ECDHE SM2 client auth noroot-ssl

[10-ECDHE SM2 client auth noroot-ssl]
server = 10-ECDHE SM2 client auth noroot-server
client = 10-ECDHE SM2 client auth noroot-client

[10-ECDHE SM2 client auth noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[10-ECDHE SM2 client auth noroot-client]
CipherString = ECDHE-SM2-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-10]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA
Method = NTLS


# ===========================================================

[11-ECDHE SM2 client encryption certificate expired]
ssl_conf = 11-ECDHE SM2 client encryption certificate expired-ssl

[11-ECDHE SM2 client encryption certificate expired-ssl]
server = 11-ECDHE SM2 client encryption certificate expired-server
client = 11-ECDHE SM2 client encryption certificate expired-client

[11-ECDHE SM2 client encryption certificate expired-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt

[11-ECDHE SM2 client encryption certificate expired-client]
CipherString = ECDHE-SM2-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_enc_expire.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-11]
ExpectedResult = ServerFail
ExpectedServerAlert = CertificateExpired
Method = NTLS


# ===========================================================

[12-ECDHE SM2 client sign certificate expired]
ssl_conf = 12-ECDHE SM2 client sign certificate expired-ssl

[12-ECDHE SM2 client sign certificate expired-ssl]
server = 12-ECDHE SM2 client sign certificate expired-server
client = 12-ECDHE SM2 client sign certificate expired-client

[12-ECDHE SM2 client sign certificate expired-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt

[12-ECDHE SM2 client sign certificate expired-client]
CipherString = ECDHE-SM2-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_sign_expire.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-12]
ExpectedResult = ServerFail
ExpectedServerAlert = CertificateExpired
Method = NTLS


# ===========================================================

[13-ECDHE SM2 client certificates expired]
ssl_conf = 13-ECDHE SM2 client certificates expired-ssl

[13-ECDHE SM2 client certificates expired-ssl]
server = 13-ECDHE SM2 client certificates expired-server
client = 13-ECDHE SM2 client certificates expired-client

[13-ECDHE SM2 client certificates expired-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt

[13-ECDHE SM2 client certificates expired-client]
CipherString = ECDHE-SM2-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_enc_expire.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/client_sign_expire.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/client_sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-13]
ExpectedResult = ServerFail
ExpectedServerAlert = CertificateExpired
Method = NTLS


# ===========================================================

[14-RSA client auth request]
ssl_conf = 14-RSA client auth request-ssl

[14-RSA client auth request-ssl]
server = 14-RSA client auth request-server
client = 14-RSA client auth request-client

[14-RSA client auth request-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.key
VerifyMode = Request

[14-RSA client auth request-client]
CipherString = RSA-SM4-CBC-SM3
Enable_ntls = on
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Peer

[test-14]
ExpectedCipher = RSA-SM4-CBC-SM3
ExpectedProtocol = NTLS
ExpectedResult = Success
Method = NTLS


# ===========================================================

[15-RSA client auth require fail]
ssl_conf = 15-RSA client auth require fail-ssl

[15-RSA client auth require fail-ssl]
server = 15-RSA client auth require fail-server
client = 15-RSA client auth require fail-client

[15-RSA client auth require fail-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.key
VerifyMode = Require

[15-RSA client auth require fail-client]
CipherString = RSA-SM4-CBC-SM3
Enable_ntls = on
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Peer

[test-15]
ExpectedResult = ServerFail
ExpectedServerAlert = HandshakeFailure
Method = NTLS


# ===========================================================

[16-RSA client auth require]
ssl_conf = 16-RSA client auth require-ssl

[16-RSA client auth require-ssl]
server = 16-RSA client auth require-server
client = 16-RSA client auth require-client

[16-RSA client auth require-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[16-RSA client auth require-client]
CipherString = RSA-SM4-CBC-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/client-rsa-enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/client-rsa-enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/client-rsa-sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/client-rsa-sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Peer

[test-16]
ExpectedCipher = RSA-SM4-CBC-SM3
ExpectedClientCANames = empty
ExpectedClientCertType = RSA
ExpectedProtocol = NTLS
ExpectedResult = Success
Method = NTLS


# ===========================================================

[17-RSA client auth non empty names]
ssl_conf = 17-RSA client auth non empty names-ssl

[17-RSA client auth non empty names-ssl]
server = 17-RSA client auth non empty names-server
client = 17-RSA client auth non empty names-client

[17-RSA client auth non empty names-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
ClientCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[17-RSA client auth non empty names-client]
CipherString = RSA-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/client-rsa-enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/client-rsa-enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/client-rsa-sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/client-rsa-sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Peer

[test-17]
ExpectedCipher = RSA-SM4-GCM-SM3
ExpectedClientCANames = ${ENV::TEST_CERTS_DIR}/root-cert.pem
ExpectedClientCertType = RSA
ExpectedProtocol = NTLS
ExpectedResult = Success
Method = NTLS


# ===========================================================

[18-RSA client auth noroot]
ssl_conf = 18-RSA client auth noroot-ssl

[18-RSA client auth noroot-ssl]
server = 18-RSA client auth noroot-server
client = 18-RSA client auth noroot-client

[18-RSA client auth noroot-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.key
VerifyMode = Require

[18-RSA client auth noroot-client]
CipherString = RSA-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/client-rsa-enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/client-rsa-enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/client-rsa-sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/client-rsa-sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Peer

[test-18]
ExpectedResult = ServerFail
ExpectedServerAlert = UnknownCA
Method = NTLS


# ===========================================================

[19-RSA client encryption certificate expired]
ssl_conf = 19-RSA client encryption certificate expired-ssl

[19-RSA client encryption certificate expired-ssl]
server = 19-RSA client encryption certificate expired-server
client = 19-RSA client encryption certificate expired-client

[19-RSA client encryption certificate expired-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[19-RSA client encryption certificate expired-client]
CipherString = RSA-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/client-rsa-enc-expired.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/client-rsa-enc-expired.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/client-rsa-sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/client-rsa-sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Peer

[test-19]
ExpectedResult = ServerFail
ExpectedServerAlert = CertificateExpired
Method = NTLS


# ===========================================================

[20-RSA client sign certificate expired]
ssl_conf = 20-RSA client sign certificate expired-ssl

[20-RSA client sign certificate expired-ssl]
server = 20-RSA client sign certificate expired-server
client = 20-RSA client sign certificate expired-client

[20-RSA client sign certificate expired-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[20-RSA client sign certificate expired-client]
CipherString = RSA-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/client-rsa-enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/client-rsa-enc.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/client-rsa-sign-expired.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/client-rsa-sign-expired.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Peer

[test-20]
ExpectedResult = ServerFail
ExpectedServerAlert = CertificateExpired
Method = NTLS


# ===========================================================

[21-RSA client certificates expired]
ssl_conf = 21-RSA client certificates expired-ssl

[21-RSA client certificates expired-ssl]
server = 21-RSA client certificates expired-server
client = 21-RSA client certificates expired-client

[21-RSA client certificates expired-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-enc.key
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/server-rsa-sign.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Require

[21-RSA client certificates expired-client]
CipherString = RSA-SM4-GCM-SM3
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/client-rsa-enc-expired.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/client-rsa-enc-expired.key
SignCertificate = ${ENV::TEST_CERTS_DIR}/client-rsa-sign-expired.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/client-rsa-sign-expired.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/root-cert.pem
VerifyMode = Peer

[test-21]
ExpectedResult = ServerFail
ExpectedServerAlert = CertificateExpired
Method = NTLS


