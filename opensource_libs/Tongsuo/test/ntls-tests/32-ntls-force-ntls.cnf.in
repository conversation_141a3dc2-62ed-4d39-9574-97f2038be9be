# -*- mode: perl; -*-
# Copyright 2023 The Tongsuo Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://github.com/Tongsuo-Project/Tongsuo/blob/master/LICENSE.txt

## Test NTLS handshake

use strict;
use warnings;

package ssltests;
use OpenSSL::Test::Utils;

our @tests = (

    {
        name => "test ntls client handshake with server which has set enable_force_ntls",
        server => {
            "SignCertificate" => test_pem("sm2", "server_sign.crt"),
            "SignPrivateKey" => test_pem("sm2", "server_sign.key"),
            "EncCertificate" => test_pem("sm2", "server_enc.crt"),
            "EncPrivateKey" => test_pem("sm2", "server_enc.key"),
            "Enable_ntls" => "on",
            "Enable_force_ntls" => "on",
        },
        client => {
            "VerifyCAFile" => test_pem("sm2", "chain-ca.crt"),
            "Enable_ntls" => "on",
        },
        test   => {
            "Method" => "NTLS",
            "ExpectedResult" => "Success",
            "ExpectedProtocol" => "NTLS",
        },
    },
    {
        name => "test tls client handshake with server which has set enable_force_ntls",
        server => {
            "SignCertificate" => test_pem("sm2", "server_sign.crt"),
            "SignPrivateKey" => test_pem("sm2", "server_sign.key"),
            "EncCertificate" => test_pem("sm2", "server_enc.crt"),
            "EncPrivateKey" => test_pem("sm2", "server_enc.key"),
            "Enable_ntls" => "on",
            "Enable_force_ntls" => "on",
        },
        client => {
            "Enable_ntls" => "off",
        },
        test   => {
            "Method" => "TLS",
            "ExpectedResult" => "ServerFail",
        },
    },
);
