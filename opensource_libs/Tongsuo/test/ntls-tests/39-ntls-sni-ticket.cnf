# Generated with generate_ssl_tests.pl

num_tests = 17

test-0 = 0-ntls-sni-session-ticket
test-1 = 1-ntls-sni-session-ticket
test-2 = 2-ntls-sni-session-ticket
test-3 = 3-ntls-sni-session-ticket
test-4 = 4-ntls-sni-session-ticket
test-5 = 5-ntls-sni-session-ticket
test-6 = 6-ntls-sni-session-ticket
test-7 = 7-ntls-sni-session-ticket
test-8 = 8-ntls-sni-session-ticket
test-9 = 9-ntls-sni-session-ticket
test-10 = 10-ntls-sni-session-ticket
test-11 = 11-ntls-sni-session-ticket
test-12 = 12-ntls-sni-session-ticket
test-13 = 13-ntls-sni-session-ticket
test-14 = 14-ntls-sni-session-ticket
test-15 = 15-ntls-sni-session-ticket
test-16 = 16-ntls-sni-session-ticket
# ===========================================================

[0-ntls-sni-session-ticket]
ssl_conf = 0-ntls-sni-session-ticket-ssl

[0-ntls-sni-session-ticket-ssl]
server = 0-ntls-sni-session-ticket-server
client = 0-ntls-sni-session-ticket-client
server2 = 0-ntls-sni-session-ticket-server2

[0-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[0-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[0-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-0]
ExpectedProtocol = NTLS
ExpectedResult = Success
Method = NTLS
SessionTicketExpected = No
server = 0-ntls-sni-session-ticket-server-extra
client = 0-ntls-sni-session-ticket-client-extra

[0-ntls-sni-session-ticket-server-extra]
BrokenSessionTicket = Yes

[0-ntls-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[1-ntls-sni-session-ticket]
ssl_conf = 1-ntls-sni-session-ticket-ssl

[1-ntls-sni-session-ticket-ssl]
server = 1-ntls-sni-session-ticket-server
client = 1-ntls-sni-session-ticket-client
server2 = 1-ntls-sni-session-ticket-server2

[1-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[1-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[1-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-1]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server1
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = Yes
server = 1-ntls-sni-session-ticket-server-extra
client = 1-ntls-sni-session-ticket-client-extra

[1-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[1-ntls-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[2-ntls-sni-session-ticket]
ssl_conf = 2-ntls-sni-session-ticket-ssl

[2-ntls-sni-session-ticket-ssl]
server = 2-ntls-sni-session-ticket-server
client = 2-ntls-sni-session-ticket-client
server2 = 2-ntls-sni-session-ticket-server2

[2-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[2-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[2-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-2]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server2
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = Yes
server = 2-ntls-sni-session-ticket-server-extra
client = 2-ntls-sni-session-ticket-client-extra

[2-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[2-ntls-sni-session-ticket-client-extra]
ServerName = server2


# ===========================================================

[3-ntls-sni-session-ticket]
ssl_conf = 3-ntls-sni-session-ticket-ssl

[3-ntls-sni-session-ticket-ssl]
server = 3-ntls-sni-session-ticket-server
client = 3-ntls-sni-session-ticket-client
server2 = 3-ntls-sni-session-ticket-server2

[3-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[3-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[3-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-3]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server1
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = Yes
server = 3-ntls-sni-session-ticket-server-extra
client = 3-ntls-sni-session-ticket-client-extra

[3-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[3-ntls-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[4-ntls-sni-session-ticket]
ssl_conf = 4-ntls-sni-session-ticket-ssl

[4-ntls-sni-session-ticket-ssl]
server = 4-ntls-sni-session-ticket-server
client = 4-ntls-sni-session-ticket-client
server2 = 4-ntls-sni-session-ticket-server2

[4-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[4-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[4-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-4]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server2
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = No
server = 4-ntls-sni-session-ticket-server-extra
client = 4-ntls-sni-session-ticket-client-extra

[4-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[4-ntls-sni-session-ticket-client-extra]
ServerName = server2


# ===========================================================

[5-ntls-sni-session-ticket]
ssl_conf = 5-ntls-sni-session-ticket-ssl

[5-ntls-sni-session-ticket-ssl]
server = 5-ntls-sni-session-ticket-server
client = 5-ntls-sni-session-ticket-client
server2 = 5-ntls-sni-session-ticket-server2

[5-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[5-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[5-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-5]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server1
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = No
server = 5-ntls-sni-session-ticket-server-extra
client = 5-ntls-sni-session-ticket-client-extra

[5-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[5-ntls-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[6-ntls-sni-session-ticket]
ssl_conf = 6-ntls-sni-session-ticket-ssl

[6-ntls-sni-session-ticket-ssl]
server = 6-ntls-sni-session-ticket-server
client = 6-ntls-sni-session-ticket-client
server2 = 6-ntls-sni-session-ticket-server2

[6-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[6-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[6-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-6]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server2
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = No
server = 6-ntls-sni-session-ticket-server-extra
client = 6-ntls-sni-session-ticket-client-extra

[6-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[6-ntls-sni-session-ticket-client-extra]
ServerName = server2


# ===========================================================

[7-ntls-sni-session-ticket]
ssl_conf = 7-ntls-sni-session-ticket-ssl

[7-ntls-sni-session-ticket-ssl]
server = 7-ntls-sni-session-ticket-server
client = 7-ntls-sni-session-ticket-client
server2 = 7-ntls-sni-session-ticket-server2

[7-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[7-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[7-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-7]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server1
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = No
server = 7-ntls-sni-session-ticket-server-extra
client = 7-ntls-sni-session-ticket-client-extra

[7-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[7-ntls-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[8-ntls-sni-session-ticket]
ssl_conf = 8-ntls-sni-session-ticket-ssl

[8-ntls-sni-session-ticket-ssl]
server = 8-ntls-sni-session-ticket-server
client = 8-ntls-sni-session-ticket-client
server2 = 8-ntls-sni-session-ticket-server2

[8-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[8-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[8-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-8]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server2
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = No
server = 8-ntls-sni-session-ticket-server-extra
client = 8-ntls-sni-session-ticket-client-extra

[8-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[8-ntls-sni-session-ticket-client-extra]
ServerName = server2


# ===========================================================

[9-ntls-sni-session-ticket]
ssl_conf = 9-ntls-sni-session-ticket-ssl

[9-ntls-sni-session-ticket-ssl]
server = 9-ntls-sni-session-ticket-server
client = 9-ntls-sni-session-ticket-client
server2 = 9-ntls-sni-session-ticket-server2

[9-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[9-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[9-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-9]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server1
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = No
server = 9-ntls-sni-session-ticket-server-extra
client = 9-ntls-sni-session-ticket-client-extra

[9-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[9-ntls-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[10-ntls-sni-session-ticket]
ssl_conf = 10-ntls-sni-session-ticket-ssl

[10-ntls-sni-session-ticket-ssl]
server = 10-ntls-sni-session-ticket-server
client = 10-ntls-sni-session-ticket-client
server2 = 10-ntls-sni-session-ticket-server2

[10-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[10-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[10-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-10]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server2
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = No
server = 10-ntls-sni-session-ticket-server-extra
client = 10-ntls-sni-session-ticket-client-extra

[10-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[10-ntls-sni-session-ticket-client-extra]
ServerName = server2


# ===========================================================

[11-ntls-sni-session-ticket]
ssl_conf = 11-ntls-sni-session-ticket-ssl

[11-ntls-sni-session-ticket-ssl]
server = 11-ntls-sni-session-ticket-server
client = 11-ntls-sni-session-ticket-client
server2 = 11-ntls-sni-session-ticket-server2

[11-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[11-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[11-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-11]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server1
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = No
server = 11-ntls-sni-session-ticket-server-extra
client = 11-ntls-sni-session-ticket-client-extra

[11-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[11-ntls-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[12-ntls-sni-session-ticket]
ssl_conf = 12-ntls-sni-session-ticket-ssl

[12-ntls-sni-session-ticket-ssl]
server = 12-ntls-sni-session-ticket-server
client = 12-ntls-sni-session-ticket-client
server2 = 12-ntls-sni-session-ticket-server2

[12-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[12-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[12-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-12]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server2
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = No
server = 12-ntls-sni-session-ticket-server-extra
client = 12-ntls-sni-session-ticket-client-extra

[12-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[12-ntls-sni-session-ticket-client-extra]
ServerName = server2


# ===========================================================

[13-ntls-sni-session-ticket]
ssl_conf = 13-ntls-sni-session-ticket-ssl

[13-ntls-sni-session-ticket-ssl]
server = 13-ntls-sni-session-ticket-server
client = 13-ntls-sni-session-ticket-client
server2 = 13-ntls-sni-session-ticket-server2

[13-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[13-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[13-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-13]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server1
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = No
server = 13-ntls-sni-session-ticket-server-extra
client = 13-ntls-sni-session-ticket-client-extra

[13-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[13-ntls-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[14-ntls-sni-session-ticket]
ssl_conf = 14-ntls-sni-session-ticket-ssl

[14-ntls-sni-session-ticket-ssl]
server = 14-ntls-sni-session-ticket-server
client = 14-ntls-sni-session-ticket-client
server2 = 14-ntls-sni-session-ticket-server2

[14-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[14-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[14-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-14]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server2
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = No
server = 14-ntls-sni-session-ticket-server-extra
client = 14-ntls-sni-session-ticket-client-extra

[14-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[14-ntls-sni-session-ticket-client-extra]
ServerName = server2


# ===========================================================

[15-ntls-sni-session-ticket]
ssl_conf = 15-ntls-sni-session-ticket-ssl

[15-ntls-sni-session-ticket-ssl]
server = 15-ntls-sni-session-ticket-server
client = 15-ntls-sni-session-ticket-client
server2 = 15-ntls-sni-session-ticket-server2

[15-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[15-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[15-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-15]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server1
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = No
server = 15-ntls-sni-session-ticket-server-extra
client = 15-ntls-sni-session-ticket-client-extra

[15-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[15-ntls-sni-session-ticket-client-extra]
ServerName = server1


# ===========================================================

[16-ntls-sni-session-ticket]
ssl_conf = 16-ntls-sni-session-ticket-ssl

[16-ntls-sni-session-ticket-ssl]
server = 16-ntls-sni-session-ticket-server
client = 16-ntls-sni-session-ticket-client
server2 = 16-ntls-sni-session-ticket-server2

[16-ntls-sni-session-ticket-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[16-ntls-sni-session-ticket-server2]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Enable_ntls = on
EncCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.crt
EncPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_enc.key
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SignCertificate = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.crt
SignPrivateKey = ${ENV::TEST_CERTS_DIR}/sm2/server_sign.key

[16-ntls-sni-session-ticket-client]
CipherString = DEFAULT
Enable_ntls = on
Options = -SessionTicket
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2/chain-ca.crt
VerifyMode = Peer

[test-16]
ExpectedProtocol = NTLS
ExpectedResult = Success
ExpectedServerName = server2
Method = NTLS
SessionIdExpected = Yes
SessionTicketExpected = No
server = 16-ntls-sni-session-ticket-server-extra
client = 16-ntls-sni-session-ticket-client-extra

[16-ntls-sni-session-ticket-server-extra]
ServerNameCallback = IgnoreMismatch

[16-ntls-sni-session-ticket-client-extra]
ServerName = server2


