expected,description, -section,val, -ref,val, -secret,val, -cert,val, -key,val, -keypass,val, -extracerts,val, BLANK, BLANK, -digest,val, -unprotected_requests,noarg
,,,,,,,,,,,,,,,,,,,,,,
1,valid secret - wrong cert/key ignored, -section,, -ref,_PBM_REF, -secret,_PBM_SECRET, -cert,root.crt, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,-server,_SERVER_HOST:_PBM_PORT,-expect_sender,""""
0,secret missing arg, -section,,BLANK,, -secret,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,
0,wrong secret without ref, -section,,BLANK,, -secret,pass:wrong,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLAN<PERSON>,
0,wrong secret - correct cert, -section,,BLANK,, -secret,pass:wrong, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,-server,_SERVER_HOST:_PBM_PORT,-expect_sender,""""
,,,,,,,,,,,,,,,,,,,,,,
0,ref missing arg, -section,, -ref,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,
1,empty ref but correct cert, -section,, -ref,"""",BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,
1,wrong ref but correct cert, -section,, -ref,wrong,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,
,,,,,,,,,,,,,,,,,,,,,,
1,valid cert and key and keypass, -section,,BLANK,,-secret,"""", -cert,signer.crt, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,
0,cert missing arg, -section,,BLANK,,BLANK,, -cert,, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,
0,key missing arg, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,
0,keypass missing arg, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,,BLANK,,BLANK,,BLANK,,BLANK,
0,keypass empty string, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:,BLANK,,BLANK,,BLANK,,BLANK,
1,keypass no prefix, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,12345,BLANK,,BLANK,,BLANK,,BLANK,
0,keypass prefix wrong, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,:12345,BLANK,,BLANK,,BLANK,,BLANK,
0,wrong keypass, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:123456,BLANK,,BLANK,,BLANK,,BLANK,
,,,,,,,,,,,,,,,,,,,,,,
0,no cert, -section,,BLANK,,BLANK,,BLANK,, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,
0,no key, -section,,BLANK,,BLANK,, -cert,signer.crt,BLANK,, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,
0,no keypass, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12,BLANK,,BLANK,,BLANK,,BLANK,,BLANK,
0,wrong cert, -section,,BLANK,,BLANK,, -cert,trusted.crt, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,
0,cert file does not exist, -section,,BLANK,,BLANK,, -cert,idontexist, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,
0,cert file random content, -section,,BLANK,,BLANK,, -cert,random.bin, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,
0,empty cert file, -section,,BLANK,,BLANK,, -cert,empty.txt, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,
0,key file random content, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,random.bin, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,
0,random keypass file, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,file:random.bin,BLANK,,BLANK,,BLANK,,BLANK,
,,,,,,,,,,,,,,,,,,,,,,
1,correct extraCerts, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345, -extracerts,issuing.crt,BLANK,,BLANK,,BLANK,
1,extracerts big file, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345, -extracerts,big_issuing.crt,BLANK,,BLANK,,BLANK,
0,extracerts missing arg, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345, -extracerts,,BLANK,,BLANK,,BLANK,
0,extracerts empty file, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345, -extracerts,empty.txt,BLANK,,BLANK,,BLANK,
0,extracerts random content, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345, -extracerts,random.bin,BLANK,,BLANK,,BLANK,
0,extracerts file does not exist, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345, -extracerts,idontexist,BLANK,,BLANK,,BLANK,
,,,,,,,,,,,,,,,,,,,,,,
1,default sha256, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,,BLANK,,BLANK,
1,digest sha256, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,, -digest,sha256,BLANK,
1,digest sha512, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,, -digest,sha512,BLANK,
0,digest missing arg, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,, -digest,,BLANK,
0,digest non-existing, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,, -digest,idontexist,BLANK,
0,multiple digests, -section,,BLANK,,BLANK,, -cert,signer.crt, -key,signer.p12, -keypass,pass:12345,BLANK,,BLANK,, -digest,sha256 sha512,BLANK,
,,,,,,,,,,,,,,,,,,,,,,
0,unprotected request, -section,,BLANK,,BLANK,, -cert,"""", -key,"""", -keypass,"""",BLANK,,BLANK,,BLANK,, -unprotected_requests,
