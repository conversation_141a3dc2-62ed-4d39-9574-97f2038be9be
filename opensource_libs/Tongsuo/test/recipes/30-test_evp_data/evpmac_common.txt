#
# Copyright 2001-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.
# The keyword Availablein must appear before the test name if needed.

Title = HMAC tests (from RFC2104 and others)

Availablein = default
MAC = HMAC
Algorithm = MD5
Key = 0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b0b
Input = "Hi There"
Output = 9294727a3638bb1c13f48ef8158bfc9d
OutputSize = 16
BlockSize = 64

Availablein = default
MAC = HMAC
Algorithm = MD5
Key = "Jefe"
Input = "what do ya want for nothing?"
Output = 750c783e6ab0b503eaa86e310a5db738
OutputSize = 16

Availablein = default
MAC = HMAC
Algorithm = MD5
Key = AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
Input = DDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD
Output = 56be34521d144c88dbb8c733f0e8b3f6
BlockSize = 64

Title = SHA1

# HMAC tests from NIST test data

MAC = HMAC
Algorithm = SHA1
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F
Output = 5FD596EE78D5553C8FF4E72D266DFD192366DA29
OutputSize = 20
BlockSize = 64

MAC = HMAC
Algorithm = SHA1
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F10111213
Output = 4C99FF0CB1B31BD33F8431DBAF4D17FCD356A807
OutputSize = 20

MAC = HMAC
Algorithm = SHA1
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F60616263
Output = 2D51B2F7750E410584662E38F133435F4C4FD42A
BlockSize = 64

Title = SHA2

MAC = HMAC
Algorithm = SHA224
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F
Output = C7405E3AE058E8CD30B08B4140248581ED174CB34E1224BCC1EFC81B
OutputSize = 28
BlockSize = 64

MAC = HMAC
Algorithm = SHA224
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B
Output = E3D249A8CFB67EF8B7A169E9A0A599714A2CECBA65999A51BEB8FBBE

MAC = HMAC
Algorithm = SHA224
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F60616263
Output = 91C52509E5AF8531601AE6230099D90BEF88AAEFB961F4080ABC014D

MAC = HMAC
Algorithm = SHA256
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F
Output = 8BB9A1DB9806F20DF7F77B82138C7914D174D59E13DC4D0169C9057B133E1D62
OutputSize = 32
BlockSize = 64

MAC = HMAC
Algorithm = SHA256
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F
Output = A28CF43130EE696A98F14A37678B56BCFCBDD9E5CF69717FECF5480F0EBDF790

MAC = HMAC
Algorithm = SHA256
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F60616263
Output = BDCCB6C72DDEADB500AE768386CB38CC41C63DBB0878DDB9C7A38A431B78378D

MAC = HMAC
Algorithm = SHA384
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F
Output = 63C5DAA5E651847CA897C95814AB830BEDEDC7D25E83EEF9195CD45857A37F448947858F5AF50CC2B1B730DDF29671A9
OutputSize = 48
BlockSize = 128

MAC = HMAC
Algorithm = SHA384
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F
Output = 6EB242BDBB582CA17BEBFA481B1E23211464D2B7F8C20B9FF2201637B93646AF5AE9AC316E98DB45D9CAE773675EEED0

MAC = HMAC
Algorithm = SHA384
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Output = 5B664436DF69B0CA22551231A3F0A3D5B4F97991713CFA84BFF4D0792EFF96C27DCCBBB6F79B65D548B40E8564CEF594

MAC = HMAC
Algorithm = SHA512
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F
Output = FC25E240658CA785B7A811A8D3F7B4CA48CFA26A8A366BF2CD1F836B05FCB024BD36853081811D6CEA4216EBAD79DA1CFCB95EA4586B8A0CE356596A55FB1347
OutputSize = 64
BlockSize = 128

MAC = HMAC
Algorithm = SHA512
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F
Output = FD44C18BDA0BB0A6CE0E82B031BF2818F6539BD56EC00BDC10A8A2D730B3634DE2545D639B0F2CF710D0692C72A1896F1F211C2B922D1A96C392E07E7EA9FEDC

MAC = HMAC
Algorithm = SHA512
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Output = D93EC8D2DE1AD2A9957CB9B83F14E76AD6B5E0CCE285079A127D3B14BCCB7AA7286D4AC0D4CE64215F2BC9E6870B33D97438BE4AAA20CDA5C5A912B48B8E27F3

Title = SHA3

# NIST's test vectors

MAC = HMAC
Algorithm = SHA3-224
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b
Output = 332cfd59347fdb8e576e77260be4aba2d6dc53117b3bfb52c6d18c04
OutputSize = 28
BlockSize = 144

MAC = HMAC
Algorithm = SHA3-224
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f
Output = d8b733bcf66c644a12323d564e24dcf3fc75f231f3b67968359100c7

MAC = HMAC
Algorithm = SHA3-224
Input = "Sample message for keylen>blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7a8a9aaab
Output = 078695eecc227c636ad31d063a15dd05a7e819a66ec6d8de1e193e59

MAC = HMAC
Algorithm = SHA3-256
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Output = 4fe8e202c4f058e8dddc23d8c34e467343e23555e24fc2f025d598f558f67205
OutputSize = 32
BlockSize = 136

MAC = HMAC
Algorithm = SHA3-256
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f8081828384858687
Output = 68b94e2e538a9be4103bebb5aa016d47961d4d1aa906061313b557f8af2c3faa

MAC = HMAC
Algorithm = SHA3-256
Input = "Sample message for keylen>blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f909192939495969798999a9b9c9d9e9fa0a1a2a3a4a5a6a7
Output = 9bcf2c238e235c3ce88404e813bd2f3a97185ac6f238c63d6229a00b07974258

MAC = HMAC
Algorithm = SHA3-384
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f
Output = d588a3c51f3f2d906e8298c1199aa8ff6296218127f6b38a90b6afe2c5617725bc99987f79b22a557b6520db710b7f42
OutputSize = 48
BlockSize = 104

MAC = HMAC
Algorithm = SHA3-384
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f6061626364656667
Output = a27d24b592e8c8cbf6d4ce6fc5bf62d8fc98bf2d486640d9eb8099e24047837f5f3bffbe92dcce90b4ed5b1e7e44fa90

MAC = HMAC
Algorithm = SHA3-384
Input = "Sample message for keylen>blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f808182838485868788898a8b8c8d8e8f9091929394959697
Output = e5ae4c739f455279368ebf36d4f5354c95aa184c899d3870e460ebc288ef1f9470053f73f7c6da2a71bcaec38ce7d6ac

MAC = HMAC
Algorithm = SHA3-512
Input = "Sample message for keylen<blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f
Output = 4efd629d6c71bf86162658f29943b1c308ce27cdfa6db0d9c3ce81763f9cbce5f7ebe9868031db1a8f8eb7b6b95e5c5e3f657a8996c86a2f6527e307f0213196
OutputSize = 64
BlockSize = 72

MAC = HMAC
Algorithm = SHA3-512
Input = "Sample message for keylen=blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f4041424344454647
Output = 544e257ea2a3e5ea19a590e6a24b724ce6327757723fe2751b75bf007d80f6b360744bf1b7a88ea585f9765b47911976d3191cf83c039f5ffab0d29cc9d9b6da

MAC = HMAC by EVP_PKEY
Algorithm = SHA3-512
Input = "Sample message for keylen>blocklen"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f202122232425262728292a2b2c2d2e2f303132333435363738393a3b3c3d3e3f404142434445464748494a4b4c4d4e4f505152535455565758595a5b5c5d5e5f606162636465666768696a6b6c6d6e6f707172737475767778797a7b7c7d7e7f8081828384858687
Output = 5f464f5e5b7848e3885e49b2c385f0694985d0e38966242dc4a5fe3fea4b37d46b65ceced5dcf59438dd840bab22269f0ba7febdb9fcf74602a35666b2a32915

Title = HMAC self generated tests

MAC = HMAC
Algorithm = SHAKE128
Input = "Test that SHAKE128 fails"
Key = 000102030405060708090a0b0c0d0e0f101112131415161718191a1b1c1d1e1f
Result = MAC_INIT_ERROR


Title = CMAC tests (from FIPS module)

MAC = CMAC
Algorithm = AES-128-CBC
Key = 77A77FAF290C1FA30C683DF16BA7A77B
Input = 020683E1F0392F4CAC54318B6029259E9C553DBC4B6AD998E64D58E4E7DC2E13
Output = FBFEA41BF9740CB501F1292C21CEBB40

MAC = CMAC by EVP_PKEY
Algorithm = AES-192-CBC
Key = 7B32391369AA4CA97558095BE3C3EC862BD057CEF1E32D62
Input =
Output = E4D9340B03E67DEFD4969CC1ED3735E6

MAC = CMAC
Algorithm = AES-256-CBC
Key = 0B122AC8F34ED1FE082A3625D157561454167AC145A10BBF77C6A70596D574F1
Input = 498B53FDEC87EDCBF07097DCCDE93A084BAD7501A224E388DF349CE18959FE8485F8AD1537F0D896EA73BEDC7214713F
Output = F62C46329B41085625669BAF51DEA66A

Title = GMAC Tests (from NIST)

MAC = GMAC
Algorithm = AES-128-GCM
Key = 77BE63708971C4E240D1CB79E8D77FEB
IV = E0E00F19FED7BA0136A797F3
Input = 7A43EC1D9C0A5A78A0B16533A6213CAB
Output = 209FCC8D3675ED938E9C7166709DD946

Title = GMAC Tests (from http://www.ieee802.org/1/files/public/docs2011/bn-randall-test-vectors-0511-v1.pdf)

MAC = GMAC
Algorithm = AES-128-GCM
Key = AD7A2BD03EAC835A6F620FDCB506B345
IV = 12153524C0895E81B2C28465
Input = D609B1F056637A0D46DF998D88E5222AB2C2846512153524C0895E8108000F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F30313233340001
Output = F09478A9B09007D06F46E9B6A1DA25DD

MAC = GMAC
Algorithm = AES-256-GCM
Key = E3C08A8F06C6E3AD95A70557B23F75483CE33021A9C72B7025666204C69C0B72
IV = 12153524C0895E81B2C28465
Input = D609B1F056637A0D46DF998D88E5222AB2C2846512153524C0895E8108000F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F30313233340001
Output = 2F0BC5AF409E06D609EA8B7D0FA5EA50

MAC = GMAC
Algorithm = AES-128-GCM
Key = 071B113B0CA743FECCCF3D051F737382
IV = F0761E8DCD3D000176D457ED
Input = E20106D7CD0DF0761E8DCD3D88E5400076D457ED08000F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A0003
Output = 0C017BC73B227DFCC9BAFA1C41ACC353

MAC = GMAC
Algorithm = AES-256-GCM
Key = 691D3EE909D7F54167FD1CA0B5D769081F2BDE1AEE655FDBAB80BD5295AE6BE7
IV = F0761E8DCD3D000176D457ED
Input = E20106D7CD0DF0761E8DCD3D88E5400076D457ED08000F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A0003
Output = 35217C774BBC31B63166BCF9D4ABED07

MAC = GMAC
Algorithm = AES-128-GCM
Key = 013FE00B5F11BE7F866D0CBBC55A7A90
IV = 7CFDE9F9E33724C68932D612
Input = 84C5D513D2AAF6E5BBD2727788E523008932D6127CFDE9F9E33724C608000F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F0005
Output = 217867E50C2DAD74C28C3B50ABDF695A

MAC = GMAC
Algorithm = AES-256-GCM
Key = 83C093B58DE7FFE1C0DA926AC43FB3609AC1C80FEE1B624497EF942E2F79A823
IV = 7CFDE9F9E33724C68932D612
Input = 84C5D513D2AAF6E5BBD2727788E523008932D6127CFDE9F9E33724C608000F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F0005
Output = 6EE160E8FAECA4B36C86B234920CA975

MAC = GMAC
Algorithm = AES-128-GCM
Key = 88EE087FD95DA9FBF6725AA9D757B0CD
IV = 7AE8E2CA4EC500012E58495C
Input = 68F2E77696CE7AE8E2CA4EC588E541002E58495C08000F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D0007
Output = 07922B8EBCF10BB2297588CA4C614523

MAC = GMAC
Algorithm = AES-256-GCM
Key = 4C973DBC7364621674F8B5B89E5C15511FCED9216490FB1C1A2CAA0FFE0407E5
IV = 7AE8E2CA4EC500012E58495C
Input = 68F2E77696CE7AE8E2CA4EC588E541002E58495C08000F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D0007
Output = 00BDA1B7E87608BCBF470F12157F4C07


Title = KMAC Tests (From NIST)
MAC = KMAC128
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 00010203
Custom = ""
Output = E5780B0D3EA6F7D3A429C5706AA43A00FADBD7D49628839E3187243F456EE14E
Ctrl = xof:0
OutputSize = 32
BlockSize = 168

MAC = KMAC128
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 00010203
Custom = "My Tagged Application"
Output = 3B1FBA963CD8B0B59E8C1A6D71888B7143651AF8BA0A7070C0979E2811324AA5

MAC = KMAC128
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Custom = "My Tagged Application"
Output = 1F5B4E6CCA02209E0DCB5CA635B89A15E271ECC760071DFD805FAA38F9729230
Ctrl = size:32

MAC = KMAC256
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 00010203
Custom = "My Tagged Application"
Output = 20C570C31346F703C9AC36C61C03CB64C3970D0CFC787E9B79599D273A68D2F7F69D4CC3DE9D104A351689F27CF6F5951F0103F33F4F24871024D9C27773A8DD
OutputSize = 64
BlockSize = 136

MAC = KMAC256
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Custom = ""
Output = 75358CF39E41494E949707927CEE0AF20A3FF553904C86B08F21CC414BCFD691589D27CF5E15369CBBFF8B9A4C2EB17800855D0235FF635DA82533EC6B759B69

MAC = KMAC256
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Custom = "My Tagged Application"
Output = B58618F71F92E1D56C1B8C55DDD7CD188B97B4CA4D99831EB2699A837DA2E4D970FBACFDE50033AEA585F1A2708510C32D07880801BD182898FE476876FC8965
Ctrl = size:64

Title = KMAC XOF Tests (From NIST)

MAC = KMAC128
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 00010203
Output = CD83740BBD92CCC8CF032B1481A0F4460E7CA9DD12B08A0C4031178BACD6EC35
XOF = 1

MAC = KMAC128
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 00010203
Custom = "My Tagged Application"
Output = 31A44527B4ED9F5C6101D11DE6D26F0620AA5C341DEF41299657FE9DF1A3B16C
XOF = 1

MAC = KMAC128
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Custom = "My Tagged Application"
Output = 47026C7CD793084AA0283C253EF658490C0DB61438B8326FE9BDDF281B83AE0F
XOF = 1
Ctrl = size:32

MAC = KMAC256
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 00010203
Custom = "My Tagged Application"
Output = 1755133F1534752AAD0748F2C706FB5C784512CAB835CD15676B16C0C6647FA96FAA7AF634A0BF8FF6DF39374FA00FAD9A39E322A7C92065A64EB1FB0801EB2B
XOF = 1

MAC = KMAC256
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Custom = ""
Output = FF7B171F1E8A2B24683EED37830EE797538BA8DC563F6DA1E667391A75EDC02CA633079F81CE12A25F45615EC89972031D18337331D24CEB8F8CA8E6A19FD98B
XOF = 1

MAC = KMAC256
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Custom = "My Tagged Application"
Output = D5BE731C954ED7732846BB59DBE3A8E30F83E77A4BFF4459F2F1C2B4ECEBB8CE67BA01C62E8AB8578D2D499BD1BB276768781190020A306A97DE281DCC30305D
Ctrl = size:64
XOF = 1

Title = KMAC long customisation string (from NIST ACVP)

MAC = KMAC256
Key = 9743DBF93102FAF11227B154B8ACD16CF142671F7AA16C559A393A38B4CEF461ED29A6A328D7379C99718790E38B54CA25E9E831CBEA463EE704D1689F94629AB795DF0C77F756DA743309C0E054596BA2D9CC1768ACF7CD351D9A7EB1ABD0A3
Input = BA63AC9C711F143CCE7FF92D0322649D1BE437D805FD225C0A2879A008373EC3BCCDB09971FAD2BCE5F4347AF7E5238EF01A90ED34193D6AFC1D
Custom = "]J&/.?L/c&}p(b!X|?>i7!]CAH6P@1<R'6|uOu2Vu^kCM!$ Een^pn&Zlale){mQhKjqe,)'-fsX6:u@D6+ZA^b70A)n)LMxo:Y!62;:[hP*yLERjL@rq30+iRaD#9|"
Output = 4057EFD76A63049418AFC54559589821322B6029808A3BCAE4D49E961F909F5F667ACAD56BBCFB8033DCB4CC10AF1B53F014B8
Ctrl = size:51
XOF = 1

Title = KMAC XOF Tests via ctrl (From NIST)

MAC = KMAC128
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 00010203
Output = CD83740BBD92CCC8CF032B1481A0F4460E7CA9DD12B08A0C4031178BACD6EC35
Ctrl = xof:1

MAC = KMAC128
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 00010203
Custom = "My Tagged Application"
Output = 31A44527B4ED9F5C6101D11DE6D26F0620AA5C341DEF41299657FE9DF1A3B16C
Ctrl = xof:1

MAC = KMAC128
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Custom = "My Tagged Application"
Output = 47026C7CD793084AA0283C253EF658490C0DB61438B8326FE9BDDF281B83AE0F
Ctrl = xof:1
Ctrl = size:32

MAC = KMAC256
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 00010203
Custom = "My Tagged Application"
Output = 1755133F1534752AAD0748F2C706FB5C784512CAB835CD15676B16C0C6647FA96FAA7AF634A0BF8FF6DF39374FA00FAD9A39E322A7C92065A64EB1FB0801EB2B
Ctrl = xof:1

MAC = KMAC256
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Custom = ""
Output = FF7B171F1E8A2B24683EED37830EE797538BA8DC563F6DA1E667391A75EDC02CA633079F81CE12A25F45615EC89972031D18337331D24CEB8F8CA8E6A19FD98B
Ctrl = xof:1

MAC = KMAC256
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Custom = "My Tagged Application"
Output = D5BE731C954ED7732846BB59DBE3A8E30F83E77A4BFF4459F2F1C2B4ECEBB8CE67BA01C62E8AB8578D2D499BD1BB276768781190020A306A97DE281DCC30305D
Ctrl = size:64
Ctrl = xof:1

Title = KMAC long customisation string via ctrl (from NIST ACVP)

MAC = KMAC256
Key = 9743DBF93102FAF11227B154B8ACD16CF142671F7AA16C559A393A38B4CEF461ED29A6A328D7379C99718790E38B54CA25E9E831CBEA463EE704D1689F94629AB795DF0C77F756DA743309C0E054596BA2D9CC1768ACF7CD351D9A7EB1ABD0A3
Input = BA63AC9C711F143CCE7FF92D0322649D1BE437D805FD225C0A2879A008373EC3BCCDB09971FAD2BCE5F4347AF7E5238EF01A90ED34193D6AFC1D
Custom = "]J&/.?L/c&}p(b!X|?>i7!]CAH6P@1<R'6|uOu2Vu^kCM!$ Een^pn&Zlale){mQhKjqe,)'-fsX6:u@D6+ZA^b70A)n)LMxo:Y!62;:[hP*yLERjL@rq30+iRaD#9|"
Output = 4057EFD76A63049418AFC54559589821322B6029808A3BCAE4D49E961F909F5F667ACAD56BBCFB8033DCB4CC10AF1B53F014B8
Ctrl = size:51
Ctrl = xof:1

Title = KMAC long customisation string negative test

MAC = KMAC128
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Custom = ":abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789::abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789::abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789::abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789::"
Result = MAC_INIT_ERROR

Title = KMAC output is too large

MAC = KMAC256
Key = 404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E3F404142434445464748494A4B4C4D4E4F505152535455565758595A5B5C5D5E5F606162636465666768696A6B6C6D6E6F707172737475767778797A7B7C7D7E7F808182838485868788898A8B8C8D8E8F909192939495969798999A9B9C9D9E9FA0A1A2A3A4A5A6A7A8A9AAABACADAEAFB0B1B2B3B4B5B6B7B8B9BABBBCBDBEBFC0C1C2C3C4C5C6C7
Custom = "My Tagged Application"
Ctrl = size:2097152
Result = MAC_INIT_ERROR
