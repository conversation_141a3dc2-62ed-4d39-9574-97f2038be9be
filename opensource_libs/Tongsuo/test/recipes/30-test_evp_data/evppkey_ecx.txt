#
# Copyright 2001-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.


# Public key algorithm tests

# Private keys used for PKEY operations.

Title = X25519 test vectors (from RFC7748 6.1)

PrivateKey=Alice-25519
-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VuBCIEIHcHbQpzGKV9PBbBclGyZkXfTC+H68CZKrF3+6UduSwq
-----END PRIVATE KEY-----

PublicKey=Alice-25519-PUBLIC
-----BEGIN PUBLIC KEY-----
MCowBQYDK2VuAyEAhSDwCYkwp1R0i33ctD73Wg2/Og0mOBr066SpjqqbTmo=
-----END PUBLIC KEY-----

PrivPubKeyPair = Alice-25519:Alice-25519-PUBLIC

PrivateKey=Bob-25519
-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VuBCIEIF2rCH5iSopLeeF/i4OADuZvO7EpJhi2/Rwviyf/iODr
-----END PRIVATE KEY-----

PublicKey=Bob-25519-PUBLIC
-----BEGIN PUBLIC KEY-----
MCowBQYDK2VuAyEA3p7bfXt9wbTTW2HC7OQ1Nz+DQ8hbeGdNrfx+FG+IK08=
-----END PUBLIC KEY-----

#Raw  versions of the same keys as above

PrivateKeyRaw=Alice-25519-Raw:X25519:77076d0a7318a57d3c16c17251b26645df4c2f87ebc0992ab177fba51db92c2a

PublicKeyRaw=Alice-25519-PUBLIC-Raw:X25519:8520f0098930a754748b7ddcb43ef75a0dbf3a0d26381af4eba4a98eaa9b4e6a

PrivPubKeyPair = Alice-25519-Raw:Alice-25519-PUBLIC-Raw

PrivateKeyRaw=Bob-25519-Raw:X25519:5dab087e624a8a4b79e17f8b83800ee66f3bb1292618b6fd1c2f8b27ff88e0eb

PublicKeyRaw=Bob-25519-PUBLIC-Raw:X25519:de9edb7d7b7dc1b4d35b61c2ece435373f8343c85b78674dadfc7e146f882b4f

PrivPubKeyPair = Bob-25519:Bob-25519-PUBLIC

PrivPubKeyPair = Bob-25519-Raw:Bob-25519-PUBLIC-Raw

Derive=Alice-25519
PeerKey=Bob-25519-PUBLIC
SharedSecret=4A5D9D5BA4CE2DE1728E3BF480350F25E07E21C947D19E3376F09B3C1E161742

Derive=Bob-25519
PeerKey=Alice-25519-PUBLIC
SharedSecret=4A5D9D5BA4CE2DE1728E3BF480350F25E07E21C947D19E3376F09B3C1E161742

Derive=Alice-25519-Raw
PeerKey=Bob-25519-PUBLIC-Raw
SharedSecret=4A5D9D5BA4CE2DE1728E3BF480350F25E07E21C947D19E3376F09B3C1E161742

Derive=Bob-25519-Raw
PeerKey=Alice-25519-PUBLIC-Raw
SharedSecret=4A5D9D5BA4CE2DE1728E3BF480350F25E07E21C947D19E3376F09B3C1E161742

# Illegal sign/verify operations with X25519 key

Sign=Alice-25519
Result = KEYOP_INIT_ERROR
Reason = operation not supported for this keytype

Verify=Alice-25519
Result = KEYOP_INIT_ERROR
Reason = operation not supported for this keytype

Title = X448 test vectors (from RFC7748 6.2)

PrivateKey=Alice-448
-----BEGIN PRIVATE KEY-----
MEYCAQAwBQYDK2VvBDoEOJqPSSXRUZ9Xdc9GsEtYANTunui66LxVZdSYwo3Zybr1
dKlBl0SJc5EAY4Km8SerHZrC2MClmHJr
-----END PRIVATE KEY-----

PublicKey=Alice-448-PUBLIC
-----BEGIN PUBLIC KEY-----
MEIwBQYDK2VvAzkAmwj3zDG34+Z9ItWuoSEHSic70rg94Jxj+qc9LCLF2bvINmRy
QdlT1AxbEtqIEg1TF3+A5TLEH6A=
-----END PUBLIC KEY-----

PrivPubKeyPair = Alice-448:Alice-448-PUBLIC

PrivateKey=Bob-448
-----BEGIN PRIVATE KEY-----
MEYCAQAwBQYDK2VvBDoEOBwwanrCoOLgmQspRHDLoznmRTdysHWBHY+tDR1pJ8Eg
u17olysNPiE3TJySGwnRsDZvELZRc5kt
-----END PRIVATE KEY-----

PublicKey=Bob-448-PUBLIC
-----BEGIN PUBLIC KEY-----
MEIwBQYDK2VvAzkAPreoKbDNIPW8/AtZm2/sz22kYnEHvbDU80W0MCfYuXL8PjT7
QjKhPKcG3LV67D2uB73BxnvzNgk=
-----END PUBLIC KEY-----

PrivPubKeyPair = Bob-448:Bob-448-PUBLIC

#Raw  versions of the same keys as above

PrivateKeyRaw=Alice-448-Raw:X448:9a8f4925d1519f5775cf46b04b5800d4ee9ee8bae8bc5565d498c28dd9c9baf574a9419744897391006382a6f127ab1d9ac2d8c0a598726b

PublicKeyRaw=Alice-448-PUBLIC-Raw:X448:9b08f7cc31b7e3e67d22d5aea121074a273bd2b83de09c63faa73d2c22c5d9bbc836647241d953d40c5b12da88120d53177f80e532c41fa0

PrivPubKeyPair = Alice-448-Raw:Alice-448-PUBLIC-Raw

PrivateKeyRaw=Bob-448-Raw:X448:1c306a7ac2a0e2e0990b294470cba339e6453772b075811d8fad0d1d6927c120bb5ee8972b0d3e21374c9c921b09d1b0366f10b65173992d

PublicKeyRaw=Bob-448-PUBLIC-Raw:X448:3eb7a829b0cd20f5bcfc0b599b6feccf6da4627107bdb0d4f345b43027d8b972fc3e34fb4232a13ca706dcb57aec3dae07bdc1c67bf33609

PrivPubKeyPair = Bob-448-Raw:Bob-448-PUBLIC-Raw

PublicKeyRaw=Bob-448-PUBLIC-Raw-NonCanonical:X448:ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff

Derive=Alice-448
PeerKey=Bob-448-PUBLIC
SharedSecret=07fff4181ac6cc95ec1c16a94a0f74d12da232ce40a77552281d282bb60c0b56fd2464c335543936521c24403085d59a449a5037514a879d

Derive=Bob-448
PeerKey=Alice-448-PUBLIC
SharedSecret=07fff4181ac6cc95ec1c16a94a0f74d12da232ce40a77552281d282bb60c0b56fd2464c335543936521c24403085d59a449a5037514a879d

Derive=Alice-448-Raw
PeerKey=Bob-448-PUBLIC-Raw
SharedSecret=07fff4181ac6cc95ec1c16a94a0f74d12da232ce40a77552281d282bb60c0b56fd2464c335543936521c24403085d59a449a5037514a879d

Derive=Bob-448-Raw
PeerKey=Alice-448-PUBLIC-Raw
SharedSecret=07fff4181ac6cc95ec1c16a94a0f74d12da232ce40a77552281d282bb60c0b56fd2464c335543936521c24403085d59a449a5037514a879d

# Self-generated non-canonical
Derive=Alice-448-Raw
PeerKey=Bob-448-PUBLIC-Raw-NonCanonical
SharedSecret=66e2e682b1f8e68c809f1bb3e406bd826921d9c1a5bfbfcbab7ae72feecee63660eabd54934f3382061d17607f581a90bdac917a064959fb

# Illegal sign/verify operations with X448 key

Sign=Alice-448
Result = KEYOP_INIT_ERROR
Reason = operation not supported for this keytype

Verify=Alice-448
Result = KEYOP_INIT_ERROR
Reason = operation not supported for this keytype

Title = ED25519 tests from RFC8032

PrivateKey=ED25519-1
-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIJ1hsZ3v/VpguoRK9JLsLMREScVpezJpGXA7rAMcrn9g
-----END PRIVATE KEY-----

PrivateKey=ED25519-2
-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIEzNCJso/5banbbDRuwRTg9bijGfNaumJNqM9u1PuKb7
-----END PRIVATE KEY-----

PrivateKey=ED25519-3
-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIMWqjfQ/n4N77bdELzHct7Fm04U1B28JS4XOOi4LRFj3
-----END PRIVATE KEY-----

PrivateKey=ED25519-4
-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIPXldnzxUzGVF2MPImh2uGyBYMxYO8ATdExr8lX1zA7l
-----END PRIVATE KEY-----

PrivateKey=ED25519-5
-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIIM/5iQJI3udYux3WHUgkR6adZzsHRl1W32pAbltyj1C
-----END PRIVATE KEY-----

PublicKey=ED25519-1-PUBLIC
-----BEGIN PUBLIC KEY-----
MCowBQYDK2VwAyEA11qYAYKxCrfVS/7TyWQHOg7hcvPapiMlrwIaaPcHURo=
-----END PUBLIC KEY-----

PublicKey=ED25519-2-PUBLIC
-----BEGIN PUBLIC KEY-----
MCowBQYDK2VwAyEAPUAXw+hDiVqStwqnTRt+vJyYLM8uxJaMwM1V8Sr0Zgw=
-----END PUBLIC KEY-----

PublicKey=ED25519-3-PUBLIC
-----BEGIN PUBLIC KEY-----
MCowBQYDK2VwAyEA/FHNjmIYoaONpH7QAjDwWAgW7RO6MwOsXeuRFUiQgCU=
-----END PUBLIC KEY-----

PublicKey=ED25519-4-PUBLIC
-----BEGIN PUBLIC KEY-----
MCowBQYDK2VwAyEAJ4EX/BRMcjQPZ9DyMW6Dhs7/vyskKMnFH+98WX8dQm4=
-----END PUBLIC KEY-----

PublicKey=ED25519-5-PUBLIC
-----BEGIN PUBLIC KEY-----
MCowBQYDK2VwAyEA7Bcrk61eVjv0kyxw4SRQNMNUZ+8u/U1k6/gZaDRn4r8=
-----END PUBLIC KEY-----

#Raw versions of the ED25519-1 keys
PrivateKeyRaw=ED25519-1-Raw:ED25519:9d61b19deffd5a60ba844af492ec2cc44449c5697b326919703bac031cae7f60

PublicKeyRaw=ED25519-1-PUBLIC-Raw:ED25519:d75a980182b10ab7d54bfed3c964073a0ee172f3daa62325af021a68f707511a

PrivPubKeyPair = ED25519-1:ED25519-1-PUBLIC

PrivPubKeyPair = ED25519-1-Raw:ED25519-1-PUBLIC-Raw

OneShotDigestSign = NULL
Key = ED25519-1
Input = ""
Output = e5564300c360ac729086e2cc806e828a84877f1eb8e5d974d873e065224901555fb8821590a33bacc61e39701cf9b46bd25bf5f0595bbe24655141438e7a100b

PrivPubKeyPair = ED25519-2:ED25519-2-PUBLIC

OneShotDigestSign = NULL
Key = ED25519-2
Input = 72
Output = 92a009a9f0d4cab8720e820b5f642540a2b27b5416503f8fb3762223ebdb69da085ac1e43e15996e458f3613d0f11d8c387b2eaeb4302aeeb00d291612bb0c00

PrivPubKeyPair = ED25519-3:ED25519-3-PUBLIC

OneShotDigestSign = NULL
Key = ED25519-3
Input = af82
Output = 6291d657deec24024827e69c3abe01a30ce548a284743a445e3680d7db5ac3ac18ff9b538d16f290ae67f760984dc6594a7c15e9716ed28dc027beceea1ec40a

PrivPubKeyPair = ED25519-4:ED25519-4-PUBLIC

OneShotDigestSign = NULL
Key = ED25519-4
Input = 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
Output = 0aab4c900501b3e24d7cdf4663326a3a87df5e4843b2cbdb67cbf6e460fec350aa5371b1508f9f4528ecea23c436d94b5e8fcd4f681e30a6ac00a9704a188a03

PrivPubKeyPair = ED25519-5:ED25519-5-PUBLIC

OneShotDigestSign = NULL
Key = ED25519-5
Input = ddaf35a193617abacc417349ae20413112e6fa4e89a97ea20a9eeee64b55d39a2192992a274fc1a836ba3c23a3feebbd454d4423643ce80e2a9ac94fa54ca49f
Output = dc2a4459e7369633a52b1bf277839a00201009a3efbf3ecb69bea2186c26b58909351fc9ac90b3ecfdfbc7c66431e0303dca179c138ac17ad9bef1177331a704

# Verify test
OneShotDigestVerify = NULL
Key = ED25519-1-PUBLIC
Input = ""
Output = e5564300c360ac729086e2cc806e828a84877f1eb8e5d974d873e065224901555fb8821590a33bacc61e39701cf9b46bd25bf5f0595bbe24655141438e7a100b

# Corrupted input
OneShotDigestVerify = NULL
Key = ED25519-1-PUBLIC
Input = "bad"
Output = e5564300c360ac729086e2cc806e828a84877f1eb8e5d974d873e065224901555fb8821590a33bacc61e39701cf9b46bd25bf5f0595bbe24655141438e7a100b
Result = VERIFY_ERROR

# Corrupted signature
OneShotDigestVerify = NULL
Key = ED25519-1-PUBLIC
Input = ""
Output = e5564300c360ac729086e2cc806e828a84877f1eb8e5d974d873e065224901555fb8821590a33bacc61e39701cf9b46bd25bf5f0595bbe24655141438e7a100c
Result = VERIFY_ERROR

PrivPubKeyPair = ED25519-1:ED25519-2-PUBLIC
Result = KEYPAIR_MISMATCH

# Make sure update calls return an error
DigestSign = NULL
Key = ED25519-1
Input = "Test"
Result = DIGESTUPDATE_ERROR

DigestVerify = NULL
Key = ED25519-1-PUBLIC
Input = "Test"
Result = DIGESTUPDATE_ERROR

# Attempt to set invalid digest
DigestSign = SHA256
Key = ED25519-1
Result = DIGESTSIGNINIT_ERROR

# Raw tests

OneShotDigestSign = NULL
Key = ED25519-1-Raw
Input = ""
Output = e5564300c360ac729086e2cc806e828a84877f1eb8e5d974d873e065224901555fb8821590a33bacc61e39701cf9b46bd25bf5f0595bbe24655141438e7a100b

OneShotDigestVerify = NULL
Key = ED25519-1-PUBLIC-Raw
Input = ""
Output = e5564300c360ac729086e2cc806e828a84877f1eb8e5d974d873e065224901555fb8821590a33bacc61e39701cf9b46bd25bf5f0595bbe24655141438e7a100b

#Signature maleability test.
#Same as the verify operation above but with the order added to s
OneShotDigestVerify = NULL
Key = ED25519-1-PUBLIC-Raw
Input = ""
Output = e5564300c360ac729086e2cc806e828a84877f1eb8e5d974d873e065224901554c8c7872aa064e049dbb3013fbf29380d25bf5f0595bbe24655141438e7a101b
Result = VERIFY_ERROR

Title = ED448 tests from RFC8032

PrivateKey=ED448-1
-----BEGIN PRIVATE KEY-----
MEcCAQAwBQYDK2VxBDsEOWyCpWLLgI0Q1jK+ichRPr9skp803fqMn2PJlg7240ij
UoyKP8wvBE45o/xblEkvjwMudUmiAJj5Ww==
-----END PRIVATE KEY-----

PrivateKey=ED448-2
-----BEGIN PRIVATE KEY-----
MEcCAQAwBQYDK2VxBDsEOcTqsF01cAfGMvPbtISJkk1VKwj+DDU6DUofAKzaLEY6
++pnxejSh3xeO8OXplmUnvgCHpVOChInTg==
-----END PRIVATE KEY-----

PrivateKey=ED448-3
-----BEGIN PRIVATE KEY-----
MEcCAQAwBQYDK2VxBDsEOc0j0k9xQnTnRDQyN7kykPUR9kJfmOZEWf8gPomFCD/9
9gUAVTq8DgXNAhhL24nEzNZ+GHlRJn6zKA==
-----END PRIVATE KEY-----

PrivateKey=ED448-4
-----BEGIN PRIVATE KEY-----
MEcCAQAwBQYDK2VxBDsEOSWM3UraMu2cn/VOY3Vq5YL7j6sqxyHyyOZ2pydoUT2T
n2Pd21VgkTPymt+G7Jkp3MtSwcX9L/fiGw==
-----END PRIVATE KEY-----

PrivateKey=ED448-5
-----BEGIN PRIVATE KEY-----
MEcCAQAwBQYDK2VxBDsEOX706EVEI2dS+7VrjzGiOhDkKBT19VygN83MEcZMmjsp
ScG7YHADFGEXMqbC/qmO68AmahGpOXAQDg==
-----END PRIVATE KEY-----

PrivateKey=ED448-6
-----BEGIN PRIVATE KEY-----
MEcCAQAwBQYDK2VxBDsEOdZd80GtE+AIVnaIuu3ajp3NwX3AJJdOpbQie2Uw4zm/
8h+Z5oymlo88ym3+D7n0+rT6E11VQuo/AQ==
-----END PRIVATE KEY-----

PrivateKey=ED448-7
-----BEGIN PRIVATE KEY-----
MEcCAQAwBQYDK2VxBDsEOS7F/jwXBFq9sTal5qkT4yq3WuaLU9L8FJt35QQTLTdW
m352a6dKGb1hYjQ6IchZCqnOvKkBTGNt9Q==
-----END PRIVATE KEY-----

PrivateKey=ED448-8
-----BEGIN PRIVATE KEY-----
MEcCAQAwBQYDK2VxBDsEOYctCTeA9dNzDffCEmZLN7ig8k9WgQ2qg4LNT6P3djTs
RNxU8cLtm+qG+vt2Mti+GZ6hZfWtVd2c6A==
-----END PRIVATE KEY-----

PublicKey=ED448-1-PUBLIC
-----BEGIN PUBLIC KEY-----
MEMwBQYDK2VxAzoAX9dEm1m0Yf0s54fsYWrUah2hNCSFpw4fig6nXYDpZ3jt8SR2
m0bHBhvWeD3x5Q9s0foavq/oJWGA
-----END PUBLIC KEY-----

PublicKey=ED448-2-PUBLIC
-----BEGIN PUBLIC KEY-----
MEMwBQYDK2VxAzoAQ7oo9DDN/0Vq5TFUX37NCsg0pV2TWMA3K/oMbGeYwIZq6gHr
AHQoArhDjqTLghacI1FgYntMOpSA
-----END PUBLIC KEY-----

PublicKey=ED448-3-PUBLIC
-----BEGIN PUBLIC KEY-----
MEMwBQYDK2VxAzoA3OqeePNaG/NJmoMbELhskKrAHNhLZ6AQm1WjbpMoseNl/OFh
1xznExpUPqTLX36fHYsAaWRHABQA
-----END PUBLIC KEY-----

PublicKey=ED448-4-PUBLIC
-----BEGIN PUBLIC KEY-----
MEMwBQYDK2VxAzoAO6FtoMbyzB8wGHdAdW9eeY1rxfwBXXxjzJUQ7j/UStwk2Olo
tuRub5TRm5RTYXJr114UnvCYF/WA
-----END PUBLIC KEY-----

PublicKey=ED448-5-PUBLIC
-----BEGIN PUBLIC KEY-----
MEMwBQYDK2VxAzoAs9oHmwqkk6V3ICnwRnuuvuWoES2dOiJTI2HaKU97s4FcXcWe
F2tNnzgcoJOOE8bAexdL5l36V46A
-----END PUBLIC KEY-----

PublicKey=ED448-6-PUBLIC
-----BEGIN PUBLIC KEY-----
MEMwBQYDK2VxAzoA35cF9Y7bq4Asf4Njz+VWCrHGEywgqfHdFjSDom+KxTo51oCL
9KHfvSYbCZuwOz+1CQbLKL2KCB8A
-----END PUBLIC KEY-----

PublicKey=ED448-7-PUBLIC
-----BEGIN PUBLIC KEY-----
MEMwBQYDK2VxAzoAeXVvAU3P4gefXdnnGL5BceLvJIagjyUYb2v/Q6mTa5v+EkAr
CK5leYo9geIunsgOdpCGLvPU7ToA
-----END PUBLIC KEY-----

PublicKey=ED448-8-PUBLIC
-----BEGIN PUBLIC KEY-----
MEMwBQYDK2VxAzoAqBsuinClrJT/28ybrfw/6wgB8lhXi7EUrUTs4ewOeZ2gjv+4
HF1oXAxW9k7srvjN8RzDhzeDjPQA
-----END PUBLIC KEY-----

#Raw versions of the ED448-1 keys
PrivateKeyRaw=ED448-1-Raw:ED448:6c82a562cb808d10d632be89c8513ebf6c929f34ddfa8c9f63c9960ef6e348a3528c8a3fcc2f044e39a3fc5b94492f8f032e7549a20098f95b

PublicKeyRaw=ED448-1-PUBLIC-Raw:ED448:5fd7449b59b461fd2ce787ec616ad46a1da1342485a70e1f8a0ea75d80e96778edf124769b46c7061bd6783df1e50f6cd1fa1abeafe8256180

PrivPubKeyPair = ED448-1:ED448-1-PUBLIC

PrivPubKeyPair = ED448-2:ED448-2-PUBLIC

PrivPubKeyPair = ED448-3:ED448-3-PUBLIC

PrivPubKeyPair = ED448-4:ED448-4-PUBLIC

PrivPubKeyPair = ED448-5:ED448-5-PUBLIC

PrivPubKeyPair = ED448-6:ED448-6-PUBLIC

PrivPubKeyPair = ED448-7:ED448-7-PUBLIC

PrivPubKeyPair = ED448-8:ED448-8-PUBLIC

PrivPubKeyPair = ED448-1-Raw:ED448-1-PUBLIC-Raw

OneShotDigestSign = NULL
Key = ED448-1
Input = ""
Output = 533a37f6bbe457251f023c0d88f976ae2dfb504a843e34d2074fd823d41a591f2b233f034f628281f2fd7a22ddd47d7828c59bd0a21bfd3980ff0d2028d4b18a9df63e006c5d1c2d345b925d8dc00b4104852db99ac5c7cdda8530a113a0f4dbb61149f05a7363268c71d95808ff2e652600

OneShotDigestSign = NULL
Key = ED448-2
Input = 03
Output = 26b8f91727bd62897af15e41eb43c377efb9c610d48f2335cb0bd0087810f4352541b143c4b981b7e18f62de8ccdf633fc1bf037ab7cd779805e0dbcc0aae1cbcee1afb2e027df36bc04dcecbf154336c19f0af7e0a6472905e799f1953d2a0ff3348ab21aa4adafd1d234441cf807c03a00

OneShotDigestSign = NULL
Key = ED448-3
Input = 0c3e544074ec63b0265e0c
Output = 1f0a8888ce25e8d458a21130879b840a9089d999aaba039eaf3e3afa090a09d389dba82c4ff2ae8ac5cdfb7c55e94d5d961a29fe0109941e00b8dbdeea6d3b051068df7254c0cdc129cbe62db2dc957dbb47b51fd3f213fb8698f064774250a5028961c9bf8ffd973fe5d5c206492b140e00

OneShotDigestSign = NULL
Key = ED448-4
Input = 64a65f3cdedcdd66811e2915
Output = 7eeeab7c4e50fb799b418ee5e3197ff6bf15d43a14c34389b59dd1a7b1b85b4ae90438aca634bea45e3a2695f1270f07fdcdf7c62b8efeaf00b45c2c96ba457eb1a8bf075a3db28e5c24f6b923ed4ad747c3c9e03c7079efb87cb110d3a99861e72003cbae6d6b8b827e4e6c143064ff3c00

OneShotDigestSign = NULL
Key = ED448-5
Input = 64a65f3cdedcdd66811e2915e7
Output = 6a12066f55331b6c22acd5d5bfc5d71228fbda80ae8dec26bdd306743c5027cb4890810c162c027468675ecf645a83176c0d7323a2ccde2d80efe5a1268e8aca1d6fbc194d3f77c44986eb4ab4177919ad8bec33eb47bbb5fc6e28196fd1caf56b4e7e0ba5519234d047155ac727a1053100

OneShotDigestSign = NULL
Key = ED448-6
Input = bd0f6a3747cd561bdddf4640a332461a4a30a12a434cd0bf40d766d9c6d458e5512204a30c17d1f50b5079631f64eb3112182da3005835461113718d1a5ef944
Output = 554bc2480860b49eab8532d2a533b7d578ef473eeb58c98bb2d0e1ce488a98b18dfde9b9b90775e67f47d4a1c3482058efc9f40d2ca033a0801b63d45b3b722ef552bad3b4ccb667da350192b61c508cf7b6b5adadc2c8d9a446ef003fb05cba5f30e88e36ec2703b349ca229c2670833900

OneShotDigestSign = NULL
Key = ED448-7
Input = 15777532b0bdd0d1389f636c5f6b9ba734c90af572877e2d272dd078aa1e567cfa80e12928bb542330e8409f3174504107ecd5efac61ae7504dabe2a602ede89e5cca6257a7c77e27a702b3ae39fc769fc54f2395ae6a1178cab4738e543072fc1c177fe71e92e25bf03e4ecb72f47b64d0465aaea4c7fad372536c8ba516a6039c3c2a39f0e4d832be432dfa9a706a6e5c7e19f397964ca4258002f7c0541b590316dbc5622b6b2a6fe7a4abffd96105eca76ea7b98816af0748c10df048ce012d901015a51f189f3888145c03650aa23ce894c3bd889e030d565071c59f409a9981b51878fd6fc110624dcbcde0bf7a69ccce38fabdf86f3bef6044819de11
Output = c650ddbb0601c19ca11439e1640dd931f43c518ea5bea70d3dcde5f4191fe53f00cf966546b72bcc7d58be2b9badef28743954e3a44a23f880e8d4f1cfce2d7a61452d26da05896f0a50da66a239a8a188b6d825b3305ad77b73fbac0836ecc60987fd08527c1a8e80d5823e65cafe2a3d00

OneShotDigestSign = NULL
Key = ED448-8
Input = 6ddf802e1aae4986935f7f981ba3f0351d6273c0a0c22c9c0e8339168e675412a3debfaf435ed651558007db4384b650fcc07e3b586a27a4f7a00ac8a6fec2cd86ae4bf1570c41e6a40c931db27b2faa15a8cedd52cff7362c4e6e23daec0fbc3a79b6806e316efcc7b68119bf46bc76a26067a53f296dafdbdc11c77f7777e972660cf4b6a9b369a6665f02e0cc9b6edfad136b4fabe723d2813db3136cfde9b6d044322fee2947952e031b73ab5c603349b307bdc27bc6cb8b8bbd7bd323219b8033a581b59eadebb09b3c4f3d2277d4f0343624acc817804728b25ab797172b4c5c21a22f9c7839d64300232eb66e53f31c723fa37fe387c7d3e50bdf9813a30e5bb12cf4cd930c40cfb4e1fc622592a49588794494d56d24ea4b40c89fc0596cc9ebb961c8cb10adde976a5d602b1c3f85b9b9a001ed3c6a4d3b1437f52096cd1956d042a597d561a596ecd3d1735a8d570ea0ec27225a2c4aaff26306d1526c1af3ca6d9cf5a2c98f47e1c46db9a33234cfd4d81f2c98538a09ebe76998d0d8fd25997c7d255c6d66ece6fa56f11144950f027795e653008f4bd7ca2dee85d8e90f3dc315130ce2a00375a318c7c3d97be2c8ce5b6db41a6254ff264fa6155baee3b0773c0f497c573f19bb4f4240281f0b1f4f7be857a4e59d416c06b4c50fa09e1810ddc6b1467baeac5a3668d11b6ecaa901440016f389f80acc4db977025e7f5924388c7e340a732e554440e76570f8dd71b7d640b3450d1fd5f0410a18f9a3494f707c717b79b4bf75c98400b096b21653b5d217cf3565c9597456f70703497a078763829bc01bb1cbc8fa04eadc9a6e3f6699587a9e75c94e5bab0036e0b2e711392cff0047d0d6b05bd2a588bc109718954259f1d86678a579a3120f19cfb2963f177aeb70f2d4844826262e51b80271272068ef5b3856fa8535aa2a88b2d41f2a0e2fda7624c2850272ac4a2f561f8f2f7a318bfd5caf9696149e4ac824ad3460538fdc25421beec2cc6818162d06bbed0c40a387192349db67a118bada6cd5ab0140ee273204f628aad1c135f770279a651e24d8c14d75a6059d76b96a6fd857def5e0b354b27ab937a5815d16b5fae407ff18222c6d1ed263be68c95f32d908bd895cd76207ae726487567f9a67dad79abec316f683b17f2d02bf07e0ac8b5bc6162cf94697b3c27cd1fea49b27f23ba2901871962506520c392da8b6ad0d99f7013fbc06c2c17a569500c8a7696481c1cd33e9b14e40b82e79a5f5db82571ba97bae3ad3e0479515bb0e2b0f3bfcd1fd33034efc6245eddd7ee2086ddae2600d8ca73e214e8c2b0bdb2b047c6a464a562ed77b73d2d841c4b34973551257713b753632efba348169abc90a68f42611a40126d7cb21b58695568186f7e569d2ff0f9e745d0487dd2eb997cafc5abf9dd102e62ff66cba87
Output = e301345a41a39a4d72fff8df69c98075a0cc082b802fc9b2b6bc503f926b65bddf7f4c8f1cb49f6396afc8a70abe6d8aef0db478d4c6b2970076c6a0484fe76d76b3a97625d79f1ce240e7c576750d295528286f719b413de9ada3e8eb78ed573603ce30d8bb761785dc30dbc320869e1a00

# Verify test
OneShotDigestVerify = NULL
Key = ED448-1-PUBLIC
Input = ""
Output = 533a37f6bbe457251f023c0d88f976ae2dfb504a843e34d2074fd823d41a591f2b233f034f628281f2fd7a22ddd47d7828c59bd0a21bfd3980ff0d2028d4b18a9df63e006c5d1c2d345b925d8dc00b4104852db99ac5c7cdda8530a113a0f4dbb61149f05a7363268c71d95808ff2e652600

# Corrupted input
OneShotDigestVerify = NULL
Key = ED448-1-PUBLIC
Input = "bad"
Output = 533a37f6bbe457251f023c0d88f976ae2dfb504a843e34d2074fd823d41a591f2b233f034f628281f2fd7a22ddd47d7828c59bd0a21bfd3980ff0d2028d4b18a9df63e006c5d1c2d345b925d8dc00b4104852db99ac5c7cdda8530a113a0f4dbb61149f05a7363268c71d95808ff2e652600
Result = VERIFY_ERROR

# Corrupted signature
OneShotDigestVerify = NULL
Key = ED448-1-PUBLIC
Input = ""
Output = 533a37f6bbe457251f023c0d88f976ae2dfb504a843e34d2074fd823d41a591f2b233f034f628281f2fd7a22ddd47d7828c59bd0a21bfd3980ff0d2028d4b18a9df63e006c5d1c2d345b925d8dc00b4104852db99ac5c7cdda8530a113a0f4dbb61149f05a7363268c71d95808ff2e652601
Result = VERIFY_ERROR

# Make sure update calls return an error
DigestSign = NULL
Key = ED448-1
Input = "Test"
Result = DIGESTUPDATE_ERROR

DigestVerify = NULL
Key = ED448-1-PUBLIC
Input = "Test"
Result = DIGESTUPDATE_ERROR

# Attempt to set invalid digest
DigestSign = SHA256
Key = ED448-1
Result = DIGESTSIGNINIT_ERROR

# Raw keys
OneShotDigestSign = NULL
Key = ED448-1-Raw
Input = ""
Output = 533a37f6bbe457251f023c0d88f976ae2dfb504a843e34d2074fd823d41a591f2b233f034f628281f2fd7a22ddd47d7828c59bd0a21bfd3980ff0d2028d4b18a9df63e006c5d1c2d345b925d8dc00b4104852db99ac5c7cdda8530a113a0f4dbb61149f05a7363268c71d95808ff2e652600

OneShotDigestVerify = NULL
Key = ED448-1-PUBLIC-Raw
Input = ""
Output = 533a37f6bbe457251f023c0d88f976ae2dfb504a843e34d2074fd823d41a591f2b233f034f628281f2fd7a22ddd47d7828c59bd0a21bfd3980ff0d2028d4b18a9df63e006c5d1c2d345b925d8dc00b4104852db99ac5c7cdda8530a113a0f4dbb61149f05a7363268c71d95808ff2e652600

#Signature malelability test.
#Same as the verify operation above but with the order added to s
OneShotDigestVerify = NULL
Key = ED448-1-PUBLIC-Raw
Input = ""
Output = 533a37f6bbe457251f023c0d88f976ae2dfb504a843e34d2074fd823d41a591f2b233f034f628281f2fd7a22ddd47d7828c59bd0a21bfd3980f25278d3667403c14bcec5f9cfde9955ebc8333c0ae78fc86e518317c5c7cdda8530a113a0f4dbb61149f05a7363268c71d95808ff2e656600
Result = VERIFY_ERROR


Title = Chosen Wycheproof vectors

PrivateKeyRaw = WychePRIVATE0:X25519:288796bc5aff4b81a37501757bc0753a3c21964790d38699308debc17a6eaf8d

PublicKeyRaw = WychePUBLIC0:X25519:f0ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f

Derive=WychePRIVATE0
PeerKey=WychePUBLIC0
SharedSecret=b4e0dd76da7b071728b61f856771aa356e57eda78a5b1655cc3820fb5f854c5c

PrivateKeyRaw = WychePRIVATE1:X25519:60887b3dc72443026ebedbbbb70665f42b87add1440e7768fbd7e8e2ce5f639d

PublicKeyRaw = WychePUBLIC1:X25519:f0ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff

Derive=WychePRIVATE1
PeerKey=WychePUBLIC1
SharedSecret=38d6304c4a7e6d9f7959334fb5245bd2c754525d4c91db950206926234c1f633

PrivateKeyRaw = WychePRIVATE2:X25519:a0a4f130b98a5be4b1cedb7cb85584a3520e142d474dc9ccb909a073a976bf63

PublicKeyRaw = WychePUBLIC2:X25519:0ab4e76380d84dde4f6833c58f2a9fb8f83bb0169b172be4b6e0592887741a36

Derive=WychePRIVATE2
PeerKey=WychePUBLIC2
SharedSecret=0200000000000000000000000000000000000000000000000000000000000000

PublicKeyRaw = WychePUBLIC3:X25519:89e10d5701b4337d2d032181538b1064bd4084401ceca1fd12663a1959388000

Derive=WychePRIVATE2
PeerKey=WychePUBLIC3
SharedSecret=0900000000000000000000000000000000000000000000000000000000000000

PublicKeyRaw = WychePUBLIC4:X25519:2b55d3aa4a8f80c8c0b2ae5f933e85af49beac36c2fa7394bab76c8933f8f81d

Derive=WychePRIVATE2
PeerKey=WychePUBLIC4
SharedSecret=1000000000000000000000000000000000000000000000000000000000000000

Title = Test keypair mismatches

PrivPubKeyPair = Alice-25519:Bob-25519-PUBLIC
Result = KEYPAIR_MISMATCH

PrivPubKeyPair = Bob-25519:Alice-25519-PUBLIC
Result = KEYPAIR_MISMATCH

PrivPubKeyPair = Alice-448:Bob-448-PUBLIC
Result = KEYPAIR_MISMATCH

PrivPubKeyPair = Bob-448:Alice-448-PUBLIC
Result = KEYPAIR_MISMATCH
