#
# Copyright 2001-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.

# SIPHASH tests - default values: 2,4 rounds, 16-byte mac
# There are no official test vectors, they are simple vectors 1, 2, 3, etc

Title = SIPHASH tests

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input =
Output = a3817f04ba25a8e66df67214c7550293

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 00
Output = da87c1d86b99af44347659119b22fc45

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 0001
Output = 8177228da4a45dc7fca38bdef60affe4

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102
Output = 9c70b60c5267a94e5f33b6b02985ed51

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 00010203
Output = f88164c12d9c8faf7d0f6e7c7bcd5579

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 0001020304
Output = 1368875980776f8854527a07690e9627

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405
Output = 14eeca338b208613485ea0308fd7a15e

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 00010203040506
Output = a1f1ebbed8dbc153c0b84aa61ff08239

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 0001020304050607
Output = 3b62a9ba6258f5610f83e264f31497b4

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708
Output = 264499060ad9baabc47f8b02bb6d71ed

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E
Output = 5150d1772f50834a503e069a973fbd7c

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input =
Output = a3817f04ba25a8e66df67214c7550293

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 00
Output = da87c1d86b99af44347659119b22fc45

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 0001
Output = 8177228da4a45dc7fca38bdef60affe4

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102
Output = 9c70b60c5267a94e5f33b6b02985ed51

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 00010203
Output = f88164c12d9c8faf7d0f6e7c7bcd5579

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 0001020304
Output = 1368875980776f8854527a07690e9627

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405
Output = 14eeca338b208613485ea0308fd7a15e

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 00010203040506
Output = a1f1ebbed8dbc153c0b84aa61ff08239

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 0001020304050607
Output = 3b62a9ba6258f5610f83e264f31497b4

MAC = SipHash by EVP_PKEY
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708
Output = 264499060ad9baabc47f8b02bb6d71ed

MAC = SipHash
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E
Output = 5150d1772f50834a503e069a973fbd7c

# SIPHASH - default values: 2,4 rounds, explicit 8-byte mac

MAC = SipHash
Ctrl = size:8
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E
Output = 724506EB4C328A95

MAC = SipHash by EVP_PKEY
Ctrl = digestsize:8
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E
Output = 724506EB4C328A95

# SIPHASH - default values: 2,4 rounds, explicit 16-byte mac

MAC = SipHash
Ctrl = size:16
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E
Output = 5150d1772f50834a503e069a973fbd7c

# SIPHASH - default values: 2,4 rounds, explicit 16-byte mac (set as 0)

MAC = SipHash
Ctrl = size:0
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E
Output = 5150d1772f50834a503e069a973fbd7c

# SIPHASH - default values: 2,4 rounds, explicit 13-byte mac (invalid size)

MAC = SipHash
Ctrl = size:13
Key = 000102030405060708090A0B0C0D0E0F
Result = MAC_INIT_ERROR

# SIPHASH - default values: 2,4 rounds, explicit 13-byte mac (invalid size)
# by EVP_PKEY this time

MAC = SipHash by EVP_PKEY
Ctrl = digestsize:13
Key = 000102030405060708090A0B0C0D0E0F
Result = EVPPKEYCTXCTRL_ERROR

Title = SIPHASH - explicit rounds

MAC = SipHash
Ctrl = size:0
Ctrl = c-rounds:2
Ctrl = d-rounds:4
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E
Output = 5150d1772f50834a503e069a973fbd7c

# Generated by the reference implementation
Title = SIPHASH - non-default values: 4,8 rounds

MAC = SipHash
Ctrl = size:8
Ctrl = c-rounds:4
Ctrl = d-rounds:8
Key = 000102030405060708090A0B0C0D0E0F
Input = 000102030405060708090A0B0C0D0E0F101112131415161718191A1B1C1D1E1F202122232425262728292A2B2C2D2E2F303132333435363738393A3B3C3D3E
Output = E67784BC5503DE23
