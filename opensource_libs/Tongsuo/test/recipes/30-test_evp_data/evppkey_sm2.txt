#
# Copyright 2001-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

# Tests start with one of these keywords
#       Cipher Decrypt Derive Digest Encoding KDF MAC PBE
#       PrivPubKeyPair Sign Verify VerifyRecover
# and continue until a blank line. Lines starting with a pound sign are ignored.


# Public key algorithm tests

# Private keys used for PKEY operations.

Title = SM2 tests

PrivateKey=SM2_key1
-----BEGIN PRIVATE KEY-----
MIGHAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBG0wawIBAQQg0JFWczAXva2An9m7
2MaT9gIwWTFptvlKrxyO4TjMmbWhRANCAAQ5OirZ4n5DrKqrhaGdO4VZHhRAYVcX
Wt3Te/d/8Mr57Tf886i09VwDhSMmH8pmNq/mp6+ioUgqYG9cs6GLLioe
-----END PRIVATE KEY-----

Verify = SM2_key1
Ctrl = digest:SM3
Input = D7AD397F6FFA5D4F7F11E7217F241607DC30618C236D2C09C1B9EA8FDADEE2E8
Output = 3046022100AB1DB64DE7C40EDBDE6651C9B8EBDB804673DB836E5D5C7FE15DCF9ED2725037022100EBA714451FF69B0BB930B379E192E7CD5FA6E3C41C7FBD8303B799AB54A54621

Verify = SM2_key1
Ctrl = digest:SM3
Input = B1139602C6ECC9E15E2F3F9C635A1AFE737058BC15387479C1EA0D0B3D90E9E5
Output = 3045022100E6E0414EBD3A656C35602AF14AB20287DBF30D57AF75C49A188ED4B42391F22402202F54F277C606F4605E1CE9514947FFDDF94C67A539804A4ED17F852288BDBE2E

Verify = SM2_key1
Ctrl = digest:SHA512
Input = 40AA1B203C9D8EE150B21C3C7CDA8261492E5420C5F2B9F7380700E094C303B48E62F319C1DA0E32EB40D113C5F1749CC61AEB499167890AB82F2CC9BB706971
Output = 3046022100AE018933B9BA041784380069F2DDF609694DCD299FDBF23D09F4B711FBC103EC0221008440BB1A48C132DE4FB91BE9F43B958142FDD29FB9DABE01B17514023A2F638C
Result = PKEY_CTRL_ERROR

Availablein = default
Decrypt = SM2_key1
Input = 30818A0220466BE2EF5C11782EC77864A0055417F407A5AFC11D653C6BCE69E417BB1D05B6022062B572E21FF0DDF5C726BD3F9FF2EAE56E6294713A607E9B9525628965F62CC804203C1B5713B5DB2728EB7BF775E44F4689FC32668BDC564F52EA45B09E8DF2A5F40422084A9D0CC2997092B7D3C404FCE95956EB604D732B2307A8E5B8900ED6608CA5B197
Output = "The floofy bunnies hop at midnight"

# This is the same as above, but with explicit digest control
Availablein = default
Decrypt = SM2_key1
Ctrl = digest:SM3
Input = 30818A0220466BE2EF5C11782EC77864A0055417F407A5AFC11D653C6BCE69E417BB1D05B6022062B572E21FF0DDF5C726BD3F9FF2EAE56E6294713A607E9B9525628965F62CC804203C1B5713B5DB2728EB7BF775E44F4689FC32668BDC564F52EA45B09E8DF2A5F40422084A9D0CC2997092B7D3C404FCE95956EB604D732B2307A8E5B8900ED6608CA5B197
Output = "The floofy bunnies hop at midnight"

# Test with an C1y value < 32 bytes in length (self generated)
Availablein = default
Decrypt = SM2_key1
Input = 3072022070DAD60CDA7C30D64CF4F278A849003581223F5324BFEC9BB329229BFFAD21A6021F18AFAB2B35459D2643243B242BE4EA80C6FA5071D2D847340CC57EB9309E5D04200B772E4DB664B2601E3B85E39C4AA8C2C1910308BE13B331E009C5A9258C29FD040B6D588BE9260A94DA18E0E6
Output = "Hello World"

# Test with an C1x and C1y valuey > 32 bytes in length, and longer plaintext (self generated)
Availablein = default
Decrypt = SM2_key1
Input = 3081DD022100CD49634BBCB21CAFFFA6D33669A5A867231CB2A942A14352EF4CAF6DC3344D54022100C35B41D4DEBB3A2735EFEE821B9EBA566BD86900176A0C06672E30EE5CC04E930420C4190A3D80D86C4BD20E99F7E4B59BF6427C6808793533EEA9591D1188EC56B50473747295470E81D951BED279AC1B86A1AFE388CD2833FA9632799EC199C7D364E5663D5A94888BB2358CFCBF6283184DE0CBC41CCEA91D24746E99D231A1DA77AFD83CDF908190ED628B7369724494568A27C782A1D1D7294BCAD80C34569ED22859896301128A8118F48924D8CCD43E998D9533
Output = "Some longer plaintext for testing SM2 decryption. Blah blah blah blah blah blah blah blah blah blah blah blah blah."

# This is a "fake" test as it does only verify that the SM2 EVP_PKEY interface
# is capable of creating a signature without failing, but it does not say
# anything about the generated signature being valid, nor does it test the
# correct implementation of the cryptosystem.
Sign = SM2_key1
Ctrl = digest:SM3
Input = D7AD397F6FFA5D4F7F11E7217F241607DC30618C236D2C09C1B9EA8FDADEE2E8
Output = 3045022100f11bf36e75bb304f094fb42a4ca22377d0cc768637c5011cd59fb9ed4b130c98022035545ffe2c2efb3abee4fee661468946d886004fae8ea5311593e48f7fe21b91
Result = KEYOP_MISMATCH

Title = SM2 key generation tests

KeyGen = SM2
KeyName = SM2_genkey1

KeyGen = SM2
Ctrl = group:sm2
KeyName = SM2_genkey2
