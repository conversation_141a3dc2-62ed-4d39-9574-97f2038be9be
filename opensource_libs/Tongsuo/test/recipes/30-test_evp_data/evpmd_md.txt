#
# Copyright 2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

Title =  MD5 tests

Digest = MD5
Input =
Output = d41d8cd98f00b204e9800998ecf8427e

Digest = MD5
Input = 61
Output = 0cc175b9c0f1b6a831c399e269772661

Digest = MD5
Input = 616263
Output = 900150983cd24fb0d6963f7d28e17f72

Digest = MD5
Input = 6d65737361676520646967657374
Output = f96b697d7cb7938d525a2f31aaf161d0

Digest = MD5
Input = 6162636465666768696a6b6c6d6e6f707172737475767778797a
Output = c3fcd3d76192e4007dfb496cca67e13b

Digest = MD5
Input = 4142434445464748494a4b4c4d4e4f505152535455565758595a6162636465666768696a6b6c6d6e6f707172737475767778797a30313233343536373839
Output = d174ab98d277d9f5a5611c2c9f419d9f

Digest = MD5
Input = 3132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930313233343536373839303132333435363738393031323334353637383930
Output = 57edf4a22be3c955ac49da2e2107b67a

Title = MD5-SHA1

Digest = MD5-SHA1
Input =
Output = d41d8cd98f00b204e9800998ecf8427eda39a3ee5e6b4b0d3255bfef95601890afd80709

Digest = MD5-SHA1
Input = "abc"
Output = 900150983cd24fb0d6963f7d28e17f72a9993e364706816aba3e25717850c26c9cd0d89d

Digest = MD5-SHA1
Input = "abcdbcdecdefdefgefghfghighijhijkijkljklmklmnlmnomnopnopq"
Output = 8215ef0796a20bcaaae116d3876c664a84983e441c3bd26ebaae4aa1f95129e5e54670f1
