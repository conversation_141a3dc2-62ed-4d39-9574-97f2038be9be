# -*- mode: perl; -*-
# Copyright 2016-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## Test version negotiation

use strict;
use warnings;

package ssltests;


our @tests = (
    {
        name => "ct-permissive-without-scts",
        server => { },
        client => {
            extra => {
                "CTValidation" => "Permissive",
            },
        },
        test => {
            "ExpectedResult" => "Success",
        },
    },
    {
        name => "ct-permissive-with-scts",
        server => {
            "Certificate" => test_pem("embeddedSCTs1.pem"),
            "PrivateKey"  => test_pem("embeddedSCTs1-key.pem"),
        },
        client => {
            "VerifyCAFile" => test_pem("embeddedSCTs1_issuer.pem"),
            extra => {
                "CTValidation" => "Permissive",
            },
        },
        test => {
            "ExpectedResult" => "Success",
        },
    },
    {
        name => "ct-strict-without-scts",
        server => { },
        client => {
            extra => {
                "CTValidation" => "Strict",
            },
        },
        test => {
            "ExpectedResult" => "ClientFail",
            "ExpectedClientAlert" => "HandshakeFailure",
        },
    },
    {
        name => "ct-strict-with-scts",
        server => {
            "Certificate" => test_pem("embeddedSCTs1.pem"),
            "PrivateKey"  => test_pem("embeddedSCTs1-key.pem"),
        },
        client => {
            "VerifyCAFile" => test_pem("embeddedSCTs1_issuer.pem"),
            extra => {
                "CTValidation" => "Strict",
            },
        },
        test => {
            "ExpectedResult" => "Success",
        },
    },
    {
        name => "ct-permissive-resumption",
        server => {
            "Certificate" => test_pem("embeddedSCTs1.pem"),
            "PrivateKey"  => test_pem("embeddedSCTs1-key.pem"),
        },
        client => {
            "VerifyCAFile" => test_pem("embeddedSCTs1_issuer.pem"),
            extra => {
                "CTValidation" => "Permissive",
            },
        },
        test => {
            "HandshakeMode" => "Resume",
            "ResumptionExpected" => "Yes",
            "ExpectedResult" => "Success",
        },
    },
    {
        name => "ct-strict-resumption",
        server => {
            "Certificate" => test_pem("embeddedSCTs1.pem"),
            "PrivateKey"  => test_pem("embeddedSCTs1-key.pem"),
        },
        client => {
            "VerifyCAFile" => test_pem("embeddedSCTs1_issuer.pem"),
            extra => {
                "CTValidation" => "Strict",
            },
        },
        # SCTs are not present during resumption, so the resumption
        # should succeed.
        resume_client => {
            extra => {
                "CTValidation" => "Strict",
            },
        },
        test => {
            "HandshakeMode" => "Resume",
            "ResumptionExpected" => "Yes",
            "ExpectedResult" => "Success",
        },
    },
);
