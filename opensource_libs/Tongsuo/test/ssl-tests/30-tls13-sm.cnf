# Generated with generate_ssl_tests.pl

num_tests = 22

test-0 = 0-test ciphersuites TLS_SM4_GCM_SM3
test-1 = 1-test series of ciphersuites includes TLS_SM4_GCM_SM3
test-2 = 2-test ciphersuites TLS_SM4_CCM_SM3
test-3 = 3-test series of ciphersuites includes TLS_SM4_CCM_SM3
test-4 = 4-test sm2-sm3 signature working well in TLS_AES_128_GCM_SHA256
test-5 = 5-test sm2-sm3 signature working well in TLS_AES_256_GCM_SHA384
test-6 = 6-test server can not accept TLS_SM4_GCM_SM3 with ecdsa cert when enable sm_tls13_strict tag
test-7 = 7-test server accept TLS_SM4_GCM_SM3 with ecdsa cert when disable sm_tls13_strict tag
test-8 = 8-test server can not accept TLS_SM4_GCM_SM3 with rsa cert when enable sm_tls13_strict tag
test-9 = 9-test server can accept TLS_SM4_GCM_SM3 with rsa cert when disable sm_tls13_strict tag
test-10 = 10-test server can not accept TLS_SM4_CCM_SM3 with ecdsa cert when enable sm_tls13_strict tag
test-11 = 11-test server can accept TLS_SM4_CCM_SM3 with ecdsa cert when disable sm_tls13_strict tag
test-12 = 12-test server can not accept TLS_SM4_CCM_SM3 with rsa cert when enable sm_tls13_strict tag
test-13 = 13-test server can accept TLS_SM4_CCM_SM3 with rsa cert when disable sm_tls13_strict tag
test-14 = 14-test server can accept TLS_SM4_CCM_SM3 with long sm2 cert chain
test-15 = 15-test client enable sm_tls13_strict, the key_share extension must include curveSM2
test-16 = 16-test ClientHello1 no SM2 key_share, server should send HRR when enable sm_tls13_strict and choose TLS_SM4_GCM_SM3
test-17 = 17-test client should fail when enable sm_tls13_strict with ecdsa cert and TLS_SM4_GCM_SM3 cipher
test-18 = 18-test client auth fail when enable sm_tls13_strict, CertificateRequest with other signature algorithms except sm2sig_sm3
test-19 = 19-test client auth success when also enable sm_tls13_strict
test-20 = 20-test sm_tls13_strict server with sm2 and rsa certs, client signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3
test-21 = 21-test sm_tls13_strict client with sm2 and rsa certs, server signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3
# ===========================================================

[0-test ciphersuites TLS_SM4_GCM_SM3]
ssl_conf = 0-test ciphersuites TLS_SM4_GCM_SM3-ssl

[0-test ciphersuites TLS_SM4_GCM_SM3-ssl]
server = 0-test ciphersuites TLS_SM4_GCM_SM3-server
client = 0-test ciphersuites TLS_SM4_GCM_SM3-client

[0-test ciphersuites TLS_SM4_GCM_SM3-server]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-first-crt.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-first-key.pem

[0-test ciphersuites TLS_SM4_GCM_SM3-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-root-cert.pem
VerifyMode = Peer

[test-0]
ExpectedCipher = TLS_SM4_GCM_SM3
ExpectedResult = Success


# ===========================================================

[1-test series of ciphersuites includes TLS_SM4_GCM_SM3]
ssl_conf = 1-test series of ciphersuites includes TLS_SM4_GCM_SM3-ssl

[1-test series of ciphersuites includes TLS_SM4_GCM_SM3-ssl]
server = 1-test series of ciphersuites includes TLS_SM4_GCM_SM3-server
client = 1-test series of ciphersuites includes TLS_SM4_GCM_SM3-client

[1-test series of ciphersuites includes TLS_SM4_GCM_SM3-server]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-first-crt.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-first-key.pem

[1-test series of ciphersuites includes TLS_SM4_GCM_SM3-client]
CipherString = DEFAULT
Ciphersuites = TLS_AES_128_GCM_SHA256:TLS_SM4_GCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-root-cert.pem
VerifyMode = Peer

[test-1]
ExpectedCipher = TLS_SM4_GCM_SM3
ExpectedResult = Success


# ===========================================================

[2-test ciphersuites TLS_SM4_CCM_SM3]
ssl_conf = 2-test ciphersuites TLS_SM4_CCM_SM3-ssl

[2-test ciphersuites TLS_SM4_CCM_SM3-ssl]
server = 2-test ciphersuites TLS_SM4_CCM_SM3-server
client = 2-test ciphersuites TLS_SM4_CCM_SM3-client

[2-test ciphersuites TLS_SM4_CCM_SM3-server]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-first-crt.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_CCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-first-key.pem

[2-test ciphersuites TLS_SM4_CCM_SM3-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_CCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-root-cert.pem
VerifyMode = Peer

[test-2]
ExpectedCipher = TLS_SM4_CCM_SM3
ExpectedResult = Success


# ===========================================================

[3-test series of ciphersuites includes TLS_SM4_CCM_SM3]
ssl_conf = 3-test series of ciphersuites includes TLS_SM4_CCM_SM3-ssl

[3-test series of ciphersuites includes TLS_SM4_CCM_SM3-ssl]
server = 3-test series of ciphersuites includes TLS_SM4_CCM_SM3-server
client = 3-test series of ciphersuites includes TLS_SM4_CCM_SM3-client

[3-test series of ciphersuites includes TLS_SM4_CCM_SM3-server]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-first-crt.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_CCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-first-key.pem

[3-test series of ciphersuites includes TLS_SM4_CCM_SM3-client]
CipherString = DEFAULT
Ciphersuites = TLS_AES_128_GCM_SHA256:TLS_SM4_CCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-root-cert.pem
VerifyMode = Peer

[test-3]
ExpectedCipher = TLS_SM4_CCM_SM3
ExpectedResult = Success


# ===========================================================

[4-test sm2-sm3 signature working well in TLS_AES_128_GCM_SHA256]
ssl_conf = 4-test sm2-sm3 signature working well in TLS_AES_128_GCM_SHA256-ssl

[4-test sm2-sm3 signature working well in TLS_AES_128_GCM_SHA256-ssl]
server = 4-test sm2-sm3 signature working well in TLS_AES_128_GCM_SHA256-server
client = 4-test sm2-sm3 signature working well in TLS_AES_128_GCM_SHA256-client

[4-test sm2-sm3 signature working well in TLS_AES_128_GCM_SHA256-server]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-first-crt.pem
CipherString = DEFAULT
Ciphersuites = TLS_AES_128_GCM_SHA256
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-first-key.pem

[4-test sm2-sm3 signature working well in TLS_AES_128_GCM_SHA256-client]
CipherString = DEFAULT
Ciphersuites = TLS_AES_128_GCM_SHA256
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-root-cert.pem
VerifyMode = Peer

[test-4]
ExpectedCipher = TLS_AES_128_GCM_SHA256
ExpectedResult = Success


# ===========================================================

[5-test sm2-sm3 signature working well in TLS_AES_256_GCM_SHA384]
ssl_conf = 5-test sm2-sm3 signature working well in TLS_AES_256_GCM_SHA384-ssl

[5-test sm2-sm3 signature working well in TLS_AES_256_GCM_SHA384-ssl]
server = 5-test sm2-sm3 signature working well in TLS_AES_256_GCM_SHA384-server
client = 5-test sm2-sm3 signature working well in TLS_AES_256_GCM_SHA384-client

[5-test sm2-sm3 signature working well in TLS_AES_256_GCM_SHA384-server]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-first-crt.pem
CipherString = DEFAULT
Ciphersuites = TLS_AES_256_GCM_SHA384
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-first-key.pem

[5-test sm2-sm3 signature working well in TLS_AES_256_GCM_SHA384-client]
CipherString = DEFAULT
Ciphersuites = TLS_AES_256_GCM_SHA384
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-root-cert.pem
VerifyMode = Peer

[test-5]
ExpectedCipher = TLS_AES_256_GCM_SHA384
ExpectedResult = Success


# ===========================================================

[6-test server can not accept TLS_SM4_GCM_SM3 with ecdsa cert when enable sm_tls13_strict tag]
ssl_conf = 6-test server can not accept TLS_SM4_GCM_SM3 with ecdsa cert when enable sm_tls13_strict tag-ssl

[6-test server can not accept TLS_SM4_GCM_SM3 with ecdsa cert when enable sm_tls13_strict tag-ssl]
server = 6-test server can not accept TLS_SM4_GCM_SM3 with ecdsa cert when enable sm_tls13_strict tag-server
client = 6-test server can not accept TLS_SM4_GCM_SM3 with ecdsa cert when enable sm_tls13_strict tag-client

[6-test server can not accept TLS_SM4_GCM_SM3 with ecdsa cert when enable sm_tls13_strict tag-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem

[6-test server can not accept TLS_SM4_GCM_SM3 with ecdsa cert when enable sm_tls13_strict tag-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedResult = ServerFail


# ===========================================================

[7-test server accept TLS_SM4_GCM_SM3 with ecdsa cert when disable sm_tls13_strict tag]
ssl_conf = 7-test server accept TLS_SM4_GCM_SM3 with ecdsa cert when disable sm_tls13_strict tag-ssl

[7-test server accept TLS_SM4_GCM_SM3 with ecdsa cert when disable sm_tls13_strict tag-ssl]
server = 7-test server accept TLS_SM4_GCM_SM3 with ecdsa cert when disable sm_tls13_strict tag-server
client = 7-test server accept TLS_SM4_GCM_SM3 with ecdsa cert when disable sm_tls13_strict tag-client

[7-test server accept TLS_SM4_GCM_SM3 with ecdsa cert when disable sm_tls13_strict tag-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = off
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem

[7-test server accept TLS_SM4_GCM_SM3 with ecdsa cert when disable sm_tls13_strict tag-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedCipher = TLS_SM4_GCM_SM3
ExpectedResult = Success


# ===========================================================

[8-test server can not accept TLS_SM4_GCM_SM3 with rsa cert when enable sm_tls13_strict tag]
ssl_conf = 8-test server can not accept TLS_SM4_GCM_SM3 with rsa cert when enable sm_tls13_strict tag-ssl

[8-test server can not accept TLS_SM4_GCM_SM3 with rsa cert when enable sm_tls13_strict tag-ssl]
server = 8-test server can not accept TLS_SM4_GCM_SM3 with rsa cert when enable sm_tls13_strict tag-server
client = 8-test server can not accept TLS_SM4_GCM_SM3 with rsa cert when enable sm_tls13_strict tag-client

[8-test server can not accept TLS_SM4_GCM_SM3 with rsa cert when enable sm_tls13_strict tag-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-cert.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-key.pem

[8-test server can not accept TLS_SM4_GCM_SM3 with rsa cert when enable sm_tls13_strict tag-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedResult = ServerFail


# ===========================================================

[9-test server can accept TLS_SM4_GCM_SM3 with rsa cert when disable sm_tls13_strict tag]
ssl_conf = 9-test server can accept TLS_SM4_GCM_SM3 with rsa cert when disable sm_tls13_strict tag-ssl

[9-test server can accept TLS_SM4_GCM_SM3 with rsa cert when disable sm_tls13_strict tag-ssl]
server = 9-test server can accept TLS_SM4_GCM_SM3 with rsa cert when disable sm_tls13_strict tag-server
client = 9-test server can accept TLS_SM4_GCM_SM3 with rsa cert when disable sm_tls13_strict tag-client

[9-test server can accept TLS_SM4_GCM_SM3 with rsa cert when disable sm_tls13_strict tag-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-cert.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = off
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-key.pem

[9-test server can accept TLS_SM4_GCM_SM3 with rsa cert when disable sm_tls13_strict tag-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-9]
ExpectedCipher = TLS_SM4_GCM_SM3
ExpectedResult = Success


# ===========================================================

[10-test server can not accept TLS_SM4_CCM_SM3 with ecdsa cert when enable sm_tls13_strict tag]
ssl_conf = 10-test server can not accept TLS_SM4_CCM_SM3 with ecdsa cert when enable sm_tls13_strict tag-ssl

[10-test server can not accept TLS_SM4_CCM_SM3 with ecdsa cert when enable sm_tls13_strict tag-ssl]
server = 10-test server can not accept TLS_SM4_CCM_SM3 with ecdsa cert when enable sm_tls13_strict tag-server
client = 10-test server can not accept TLS_SM4_CCM_SM3 with ecdsa cert when enable sm_tls13_strict tag-client

[10-test server can not accept TLS_SM4_CCM_SM3 with ecdsa cert when enable sm_tls13_strict tag-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_CCM_SM3
Enable_sm_tls13_strict = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem

[10-test server can not accept TLS_SM4_CCM_SM3 with ecdsa cert when enable sm_tls13_strict tag-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_CCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-10]
ExpectedResult = ServerFail


# ===========================================================

[11-test server can accept TLS_SM4_CCM_SM3 with ecdsa cert when disable sm_tls13_strict tag]
ssl_conf = 11-test server can accept TLS_SM4_CCM_SM3 with ecdsa cert when disable sm_tls13_strict tag-ssl

[11-test server can accept TLS_SM4_CCM_SM3 with ecdsa cert when disable sm_tls13_strict tag-ssl]
server = 11-test server can accept TLS_SM4_CCM_SM3 with ecdsa cert when disable sm_tls13_strict tag-server
client = 11-test server can accept TLS_SM4_CCM_SM3 with ecdsa cert when disable sm_tls13_strict tag-client

[11-test server can accept TLS_SM4_CCM_SM3 with ecdsa cert when disable sm_tls13_strict tag-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_CCM_SM3
Enable_sm_tls13_strict = off
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem

[11-test server can accept TLS_SM4_CCM_SM3 with ecdsa cert when disable sm_tls13_strict tag-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_CCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-11]
ExpectedCipher = TLS_SM4_CCM_SM3
ExpectedResult = Success


# ===========================================================

[12-test server can not accept TLS_SM4_CCM_SM3 with rsa cert when enable sm_tls13_strict tag]
ssl_conf = 12-test server can not accept TLS_SM4_CCM_SM3 with rsa cert when enable sm_tls13_strict tag-ssl

[12-test server can not accept TLS_SM4_CCM_SM3 with rsa cert when enable sm_tls13_strict tag-ssl]
server = 12-test server can not accept TLS_SM4_CCM_SM3 with rsa cert when enable sm_tls13_strict tag-server
client = 12-test server can not accept TLS_SM4_CCM_SM3 with rsa cert when enable sm_tls13_strict tag-client

[12-test server can not accept TLS_SM4_CCM_SM3 with rsa cert when enable sm_tls13_strict tag-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-cert.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_CCM_SM3
Enable_sm_tls13_strict = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-key.pem

[12-test server can not accept TLS_SM4_CCM_SM3 with rsa cert when enable sm_tls13_strict tag-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_CCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-12]
ExpectedResult = ServerFail


# ===========================================================

[13-test server can accept TLS_SM4_CCM_SM3 with rsa cert when disable sm_tls13_strict tag]
ssl_conf = 13-test server can accept TLS_SM4_CCM_SM3 with rsa cert when disable sm_tls13_strict tag-ssl

[13-test server can accept TLS_SM4_CCM_SM3 with rsa cert when disable sm_tls13_strict tag-ssl]
server = 13-test server can accept TLS_SM4_CCM_SM3 with rsa cert when disable sm_tls13_strict tag-server
client = 13-test server can accept TLS_SM4_CCM_SM3 with rsa cert when disable sm_tls13_strict tag-client

[13-test server can accept TLS_SM4_CCM_SM3 with rsa cert when disable sm_tls13_strict tag-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-pss-cert.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_CCM_SM3
Enable_sm_tls13_strict = off
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-pss-key.pem

[13-test server can accept TLS_SM4_CCM_SM3 with rsa cert when disable sm_tls13_strict tag-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_CCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-13]
ExpectedCipher = TLS_SM4_CCM_SM3
ExpectedResult = Success


# ===========================================================

[14-test server can accept TLS_SM4_CCM_SM3 with long sm2 cert chain]
ssl_conf = 14-test server can accept TLS_SM4_CCM_SM3 with long sm2 cert chain-ssl

[14-test server can accept TLS_SM4_CCM_SM3 with long sm2 cert chain-ssl]
server = 14-test server can accept TLS_SM4_CCM_SM3 with long sm2 cert chain-server
client = 14-test server can accept TLS_SM4_CCM_SM3 with long sm2 cert chain-client

[14-test server can accept TLS_SM4_CCM_SM3 with long sm2 cert chain-server]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-leaf.crt
CipherString = DEFAULT
Ciphersuites = TLS_SM4_CCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-leaf.key

[14-test server can accept TLS_SM4_CCM_SM3 with long sm2 cert chain-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_CCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-chain-ca.crt
VerifyMode = Peer

[test-14]
ExpectedCipher = TLS_SM4_CCM_SM3
ExpectedResult = Success


# ===========================================================

[15-test client enable sm_tls13_strict, the key_share extension must include curveSM2]
ssl_conf = 15-test client enable sm_tls13_strict, the key_share extension must include curveSM2-ssl

[15-test client enable sm_tls13_strict, the key_share extension must include curveSM2-ssl]
server = 15-test client enable sm_tls13_strict, the key_share extension must include curveSM2-server
client = 15-test client enable sm_tls13_strict, the key_share extension must include curveSM2-client

[15-test client enable sm_tls13_strict, the key_share extension must include curveSM2-server]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-leaf.crt
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = off
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-leaf.key

[15-test client enable sm_tls13_strict, the key_share extension must include curveSM2-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-chain-ca.crt
VerifyMode = Peer

[test-15]
ExpectedCipher = TLS_SM4_GCM_SM3
ExpectedClientKeyShare = SM2
ExpectedResult = Success


# ===========================================================

[16-test ClientHello1 no SM2 key_share, server should send HRR when enable sm_tls13_strict and choose TLS_SM4_GCM_SM3]
ssl_conf = 16-test ClientHello1 no SM2 key_share, server should send HRR when enable sm_tls13_strict and choose TLS_SM4_GCM_SM3-ssl

[16-test ClientHello1 no SM2 key_share, server should send HRR when enable sm_tls13_strict and choose TLS_SM4_GCM_SM3-ssl]
server = 16-test ClientHello1 no SM2 key_share, server should send HRR when enable sm_tls13_strict and choose TLS_SM4_GCM_SM3-server
client = 16-test ClientHello1 no SM2 key_share, server should send HRR when enable sm_tls13_strict and choose TLS_SM4_GCM_SM3-client

[16-test ClientHello1 no SM2 key_share, server should send HRR when enable sm_tls13_strict and choose TLS_SM4_GCM_SM3-server]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-leaf.crt
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-leaf.key

[16-test ClientHello1 no SM2 key_share, server should send HRR when enable sm_tls13_strict and choose TLS_SM4_GCM_SM3-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-chain-ca.crt
VerifyMode = Peer

[test-16]
ExpectedCipher = TLS_SM4_GCM_SM3
ExpectedHRR = Yes
ExpectedResult = Success


# ===========================================================

[17-test client should fail when enable sm_tls13_strict with ecdsa cert and TLS_SM4_GCM_SM3 cipher]
ssl_conf = 17-test client should fail when enable sm_tls13_strict with ecdsa cert and TLS_SM4_GCM_SM3 cipher-ssl

[17-test client should fail when enable sm_tls13_strict with ecdsa cert and TLS_SM4_GCM_SM3 cipher-ssl]
server = 17-test client should fail when enable sm_tls13_strict with ecdsa cert and TLS_SM4_GCM_SM3 cipher-server
client = 17-test client should fail when enable sm_tls13_strict with ecdsa cert and TLS_SM4_GCM_SM3 cipher-client

[17-test client should fail when enable sm_tls13_strict with ecdsa cert and TLS_SM4_GCM_SM3 cipher-server]
Certificate = ${ENV::TEST_CERTS_DIR}/server-ecdsa-cert.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = off
Groups = SM2
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/server-ecdsa-key.pem

[17-test client should fail when enable sm_tls13_strict with ecdsa cert and TLS_SM4_GCM_SM3 cipher-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-17]
ExpectedClientAlert = BadCertificate
ExpectedResult = ClientFail


# ===========================================================

[18-test client auth fail when enable sm_tls13_strict, CertificateRequest with other signature algorithms except sm2sig_sm3]
ssl_conf = 18-test client auth fail when enable sm_tls13_strict, CertificateRequest with other signature algorithms except sm2sig_sm3-ssl

[18-test client auth fail when enable sm_tls13_strict, CertificateRequest with other signature algorithms except sm2sig_sm3-ssl]
server = 18-test client auth fail when enable sm_tls13_strict, CertificateRequest with other signature algorithms except sm2sig_sm3-server
client = 18-test client auth fail when enable sm_tls13_strict, CertificateRequest with other signature algorithms except sm2sig_sm3-client

[18-test client auth fail when enable sm_tls13_strict, CertificateRequest with other signature algorithms except sm2sig_sm3-server]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-leaf.crt
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = off
Groups = SM2
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-leaf.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-root-cert.pem
VerifyMode = Require

[18-test client auth fail when enable sm_tls13_strict, CertificateRequest with other signature algorithms except sm2sig_sm3-client]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-first-crt.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-first-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-chain-ca.crt
VerifyMode = Peer

[test-18]
ExpectedResult = ClientFail


# ===========================================================

[19-test client auth success when also enable sm_tls13_strict]
ssl_conf = 19-test client auth success when also enable sm_tls13_strict-ssl

[19-test client auth success when also enable sm_tls13_strict-ssl]
server = 19-test client auth success when also enable sm_tls13_strict-server
client = 19-test client auth success when also enable sm_tls13_strict-client

[19-test client auth success when also enable sm_tls13_strict-server]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-leaf.crt
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-leaf.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-root-cert.pem
VerifyMode = Require

[19-test client auth success when also enable sm_tls13_strict-client]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-first-crt.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-first-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-chain-ca.crt
VerifyMode = Peer

[test-19]
ExpectedCipher = TLS_SM4_GCM_SM3
ExpectedResult = Success


# ===========================================================

[20-test sm_tls13_strict server with sm2 and rsa certs, client signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3]
ssl_conf = 20-test sm_tls13_strict server with sm2 and rsa certs, client signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3-ssl

[20-test sm_tls13_strict server with sm2 and rsa certs, client signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3-ssl]
server = 20-test sm_tls13_strict server with sm2 and rsa certs, client signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3-server
client = 20-test sm_tls13_strict server with sm2 and rsa certs, client signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3-client

[20-test sm_tls13_strict server with sm2 and rsa certs, client signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
RSA.Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
RSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem
SM2.Certificate = ${ENV::TEST_CERTS_DIR}/sm2-leaf.crt
SM2.PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-leaf.key
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-root-cert.pem
VerifyMode = Require

[20-test sm_tls13_strict server with sm2 and rsa certs, client signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3-client]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-first-crt.pem
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-first-key.pem
SignatureAlgorithms = rsa_pss_rsae_sha256:sm2sig_sm3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-chain-ca.crt
VerifyMode = Peer

[test-20]
ExpectedCipher = TLS_SM4_GCM_SM3
ExpectedResult = Success
ExpectedServerCertType = SM2


# ===========================================================

[21-test sm_tls13_strict client with sm2 and rsa certs, server signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3]
ssl_conf = 21-test sm_tls13_strict client with sm2 and rsa certs, server signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3-ssl

[21-test sm_tls13_strict client with sm2 and rsa certs, server signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3-ssl]
server = 21-test sm_tls13_strict client with sm2 and rsa certs, server signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3-server
client = 21-test sm_tls13_strict client with sm2 and rsa certs, server signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3-client

[21-test sm_tls13_strict client with sm2 and rsa certs, server signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3-server]
Certificate = ${ENV::TEST_CERTS_DIR}/sm2-leaf.crt
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = off
Groups = SM2
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-leaf.key
SignatureAlgorithms = rsa_pss_rsae_sha256:sm2sig_sm3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-root-cert.pem
VerifyMode = Require

[21-test sm_tls13_strict client with sm2 and rsa certs, server signature_algorithms with rsa_pss_rsae_sha256 and sm2sig_sm3-client]
CipherString = DEFAULT
Ciphersuites = TLS_SM4_GCM_SM3
Enable_sm_tls13_strict = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
RSA.Certificate = ${ENV::TEST_CERTS_DIR}/ee-client-chain.pem
RSA.PrivateKey = ${ENV::TEST_CERTS_DIR}/ee-key.pem
SM2.Certificate = ${ENV::TEST_CERTS_DIR}/sm2-first-crt.pem
SM2.PrivateKey = ${ENV::TEST_CERTS_DIR}/sm2-first-key.pem
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/sm2-chain-ca.crt
VerifyMode = Peer

[test-21]
ExpectedClientAlert = IllegalParameter
ExpectedResult = ClientFail


