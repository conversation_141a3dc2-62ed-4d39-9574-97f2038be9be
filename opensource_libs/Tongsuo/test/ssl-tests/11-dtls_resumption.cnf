# Generated with generate_ssl_tests.pl

num_tests = 16

test-0 = 0-resumption
test-1 = 1-resumption
test-2 = 2-resumption
test-3 = 3-resumption
test-4 = 4-resumption
test-5 = 5-resumption
test-6 = 6-resumption
test-7 = 7-resumption
test-8 = 8-resumption
test-9 = 9-resumption
test-10 = 10-resumption
test-11 = 11-resumption
test-12 = 12-resumption
test-13 = 13-resumption
test-14 = 14-resumption
test-15 = 15-resumption
# ===========================================================

[0-resumption]
ssl_conf = 0-resumption-ssl

[0-resumption-ssl]
server = 0-resumption-server
client = 0-resumption-client
resume-server = 0-resumption-resume-server
resume-client = 0-resumption-client

[0-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedProtocol = DTLSv1
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = Yes


# ===========================================================

[1-resumption]
ssl_conf = 1-resumption-ssl

[1-resumption-ssl]
server = 1-resumption-server
client = 1-resumption-client
resume-server = 1-resumption-resume-server
resume-client = 1-resumption-client

[1-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedProtocol = DTLSv1
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = Yes


# ===========================================================

[2-resumption]
ssl_conf = 2-resumption-ssl

[2-resumption-ssl]
server = 2-resumption-server
client = 2-resumption-client
resume-server = 2-resumption-resume-server
resume-client = 2-resumption-client

[2-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedProtocol = DTLSv1.2
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = No


# ===========================================================

[3-resumption]
ssl_conf = 3-resumption-ssl

[3-resumption-ssl]
server = 3-resumption-server
client = 3-resumption-client
resume-server = 3-resumption-resume-server
resume-client = 3-resumption-client

[3-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedProtocol = DTLSv1.2
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = No


# ===========================================================

[4-resumption]
ssl_conf = 4-resumption-ssl

[4-resumption-ssl]
server = 4-resumption-server
client = 4-resumption-client
resume-server = 4-resumption-resume-server
resume-client = 4-resumption-client

[4-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ExpectedProtocol = DTLSv1
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = No


# ===========================================================

[5-resumption]
ssl_conf = 5-resumption-ssl

[5-resumption-ssl]
server = 5-resumption-server
client = 5-resumption-client
resume-server = 5-resumption-resume-server
resume-client = 5-resumption-client

[5-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedProtocol = DTLSv1
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = No


# ===========================================================

[6-resumption]
ssl_conf = 6-resumption-ssl

[6-resumption-ssl]
server = 6-resumption-server
client = 6-resumption-client
resume-server = 6-resumption-resume-server
resume-client = 6-resumption-client

[6-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedProtocol = DTLSv1.2
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = Yes


# ===========================================================

[7-resumption]
ssl_conf = 7-resumption-ssl

[7-resumption-ssl]
server = 7-resumption-server
client = 7-resumption-client
resume-server = 7-resumption-resume-server
resume-client = 7-resumption-client

[7-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-resumption-resume-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedProtocol = DTLSv1.2
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = Yes


# ===========================================================

[8-resumption]
ssl_conf = 8-resumption-ssl

[8-resumption-ssl]
server = 8-resumption-server
client = 8-resumption-client
resume-server = 8-resumption-server
resume-client = 8-resumption-resume-client

[8-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[8-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedProtocol = DTLSv1
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = Yes


# ===========================================================

[9-resumption]
ssl_conf = 9-resumption-ssl

[9-resumption-ssl]
server = 9-resumption-server
client = 9-resumption-client
resume-server = 9-resumption-server
resume-client = 9-resumption-resume-client

[9-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[9-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-9]
ExpectedProtocol = DTLSv1
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = Yes


# ===========================================================

[10-resumption]
ssl_conf = 10-resumption-ssl

[10-resumption-ssl]
server = 10-resumption-server
client = 10-resumption-client
resume-server = 10-resumption-server
resume-client = 10-resumption-resume-client

[10-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[10-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[10-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-10]
ExpectedProtocol = DTLSv1.2
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = No


# ===========================================================

[11-resumption]
ssl_conf = 11-resumption-ssl

[11-resumption-ssl]
server = 11-resumption-server
client = 11-resumption-client
resume-server = 11-resumption-server
resume-client = 11-resumption-resume-client

[11-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
MinProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[11-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-11]
ExpectedProtocol = DTLSv1.2
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = No


# ===========================================================

[12-resumption]
ssl_conf = 12-resumption-ssl

[12-resumption-ssl]
server = 12-resumption-server
client = 12-resumption-client
resume-server = 12-resumption-server
resume-client = 12-resumption-resume-client

[12-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[12-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[12-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-12]
ExpectedProtocol = DTLSv1
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = No


# ===========================================================

[13-resumption]
ssl_conf = 13-resumption-ssl

[13-resumption-ssl]
server = 13-resumption-server
client = 13-resumption-client
resume-server = 13-resumption-server
resume-client = 13-resumption-resume-client

[13-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[13-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-13]
ExpectedProtocol = DTLSv1
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = No


# ===========================================================

[14-resumption]
ssl_conf = 14-resumption-ssl

[14-resumption-ssl]
server = 14-resumption-server
client = 14-resumption-client
resume-server = 14-resumption-server
resume-client = 14-resumption-resume-client

[14-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[14-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[14-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-14]
ExpectedProtocol = DTLSv1.2
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = Yes


# ===========================================================

[15-resumption]
ssl_conf = 15-resumption-ssl

[15-resumption-ssl]
server = 15-resumption-server
client = 15-resumption-client
resume-server = 15-resumption-server
resume-client = 15-resumption-resume-client

[15-resumption-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
Options = -SessionTicket
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[15-resumption-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
MinProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[15-resumption-resume-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = DTLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-15]
ExpectedProtocol = DTLSv1.2
HandshakeMode = Resume
Method = DTLS
ResumptionExpected = Yes


