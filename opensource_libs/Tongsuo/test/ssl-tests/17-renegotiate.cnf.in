# -*- mode: perl; -*-
# Copyright 2016-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## Test Renegotiation

use strict;
use warnings;

package ssltests;
use OpenSSL::Test::Utils;

our @tests = (
    {
        name => "renegotiate-client-no-resume",
        server => {
            "Options" => "NoResumptionOnRenegotiation",
            "MaxProtocol" => "TLSv1.2"
        },
        client => {},
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateClient",
            "ResumptionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "renegotiate-client-resume",
        server => {
            "MaxProtocol" => "TLSv1.2"
        },
        client => {},
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateClient",
            "ResumptionExpected" => "Yes",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "renegotiate-server-no-resume",
        server => {
            "Options" => "NoResumptionOnRenegotiation",
            "MaxProtocol" => "TLSv1.2"
        },
        client => {},
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateServer",
            "ResumptionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "renegotiate-server-resume",
        server => {
            "MaxProtocol" => "TLSv1.2"
        },
        client => {},
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateServer",
            "ResumptionExpected" => "Yes",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "renegotiate-client-auth-require",
        server => {
            "Options" => "NoResumptionOnRenegotiation",
            "MaxProtocol" => "TLSv1.2",
            "VerifyCAFile" => test_pem("root-cert.pem"),
            "VerifyMode" => "Require",
        },
        client => {
            "Certificate" => test_pem("ee-client-chain.pem"),
            "PrivateKey"  => test_pem("ee-key.pem"),
        },
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateServer",
            "ResumptionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "renegotiate-client-auth-once",
        server => {
            "Options" => "NoResumptionOnRenegotiation",
            "MaxProtocol" => "TLSv1.2",
            "VerifyCAFile" => test_pem("root-cert.pem"),
            "VerifyMode" => "Once",
        },
        client => {
            "Certificate" => test_pem("ee-client-chain.pem"),
            "PrivateKey"  => test_pem("ee-key.pem"),
        },
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateServer",
            "ResumptionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    }
);
our @tests_tls1_2 = (
    {
        name => "renegotiate-aead-to-non-aead",
        server => {
            "Options" => "NoResumptionOnRenegotiation",
        },
        client => {
            "CipherString" => "AES128-GCM-SHA256",
            "MaxProtocol" => "TLSv1.2",
            extra => {
                "RenegotiateCiphers" => "AES128-SHA"
            }
        },
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateClient",
            "ResumptionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "renegotiate-non-aead-to-aead",
        server => {
            "Options" => "NoResumptionOnRenegotiation",
        },
        client => {
            "CipherString" => "AES128-SHA",
            "MaxProtocol" => "TLSv1.2",
            extra => {
                "RenegotiateCiphers" => "AES128-GCM-SHA256"
            }
        },
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateClient",
            "ResumptionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "renegotiate-non-aead-to-non-aead",
        server => {
            "Options" => "NoResumptionOnRenegotiation",
        },
        client => {
            "CipherString" => "AES128-SHA",
            "MaxProtocol" => "TLSv1.2",
            extra => {
                "RenegotiateCiphers" => "AES256-SHA"
            }
        },
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateClient",
            "ResumptionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "renegotiate-aead-to-aead",
        server => {
            "Options" => "NoResumptionOnRenegotiation",
        },
        client => {
            "CipherString" => "AES128-GCM-SHA256",
            "MaxProtocol" => "TLSv1.2",
            extra => {
                "RenegotiateCiphers" => "AES256-GCM-SHA384"
            }
        },
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateClient",
            "ResumptionExpected" => "No",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "no-renegotiation-server-by-client",
        server => {
            "Options" => "NoRenegotiation",
            "MaxProtocol" => "TLSv1.2"
        },
        client => { },
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateClient",
            "ResumptionExpected" => "No",
            "ExpectedResult" => "ClientFail"
        }
    },
    {
        name => "no-renegotiation-server-by-server",
        server => {
            "Options" => "NoRenegotiation",
            "MaxProtocol" => "TLSv1.2"
        },
        client => { },
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateServer",
            "ResumptionExpected" => "No",
            "ExpectedResult" => "ServerFail"
        }
    },
    {
        name => "no-renegotiation-client-by-server",
        server => {
            "MaxProtocol" => "TLSv1.2"
        },
        client => {
            "Options" => "NoRenegotiation",
        },
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateServer",
            "ResumptionExpected" => "No",
            "ExpectedResult" => "ServerFail"
        }
    },
    {
        name => "no-renegotiation-client-by-client",
        server => {
            "MaxProtocol" => "TLSv1.2"
        },
        client => {
            "Options" => "NoRenegotiation",
        },
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateClient",
            "ResumptionExpected" => "No",
            "ExpectedResult" => "ClientFail"
        }
    },
    {
        name => "no-extms-on-renegotiation",
        server => {
            "MaxProtocol" => "TLSv1.2"
        },
        client => {
            "MaxProtocol" => "TLSv1.2",
            extra => {
                "RenegotiateNoExtms" => "Yes"
            }
        },
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateClient",
            "ResumptionExpected" => "No",
            "ExpectedResult" => "ServerFail"
        }
    },
    {
        name => "allow-client-renegotiation",
        server => {
            "MaxProtocol" => "TLSv1.2",
        },
        client => {
            "MaxProtocol" => "TLSv1.2"
        },
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateClient",
            "ResumptionExpected" => "Yes",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "no-client-renegotiation",
        server => {
            "MaxProtocol" => "TLSv1.2",
            "Options" => "-ClientRenegotiation"
        },
        client => {
            "MaxProtocol" => "TLSv1.2",
        },
        test => {
            "Method" => "TLS",
            "HandshakeMode" => "RenegotiateClient",
            "ResumptionExpected" => "No",
            "ExpectedResult" => "ClientFail",
            "ExpectedServerAlert" => "NoRenegotiation"
        }
    }
);

push @tests, @tests_tls1_2 unless disabled("tls1_2");
