# -*- mode: perl; -*-
# Copyright 2022 The Tongsuo Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://github.com/Tongsuo-Project/Tongsuo/blob/master/LICENSE.txt

## Test delegated credential

use strict;
use warnings;

package ssltests;

our @tests = (
    {
        name => "test client can verify server by delegated credential",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "Enable_sign_by_dc" => "on",
            "Certificate" => test_run("dc", "dc-leaf-server.crt"),
            "PrivateKey" => test_run("dc", "dc-leaf-server.key"),
            "DC" => test_run("dc", "dc-server.dc"),
            "DCKey" => test_run("dc", "dc-server.key"),
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "VerifyCAFile" => test_run("dc", "dc-chain-ca.crt"),
            "Enable_verify_peer_by_dc" => "on",
        },
        test => {
            "ExpectedResult" => "Success",
            "ExpextClientDCusage" => "VerifyPeerByDCOnly",
            "ExpextServerDCusage" => "SignByDCOnly",
        },
    },
    {
        name => "test client/server can do mtls by delegated credential",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "Enable_sign_by_dc" => "on",
            "Enable_verify_peer_by_dc" => "on",
            "Certificate" => test_run("dc", "dc-leaf-server.crt"),
            "PrivateKey" => test_run("dc", "dc-leaf-server.key"),
            "DC" => test_run("dc", "dc-server.dc"),
            "DCKey" => test_run("dc", "dc-server.key"),
            "VerifyCAFile" => test_run("dc", "dc-chain-ca.crt"),
            "VerifyMode" => "Request",
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "VerifyCAFile" => test_run("dc", "dc-chain-ca.crt"),
            "Enable_sign_by_dc" => "on",
            "Enable_verify_peer_by_dc" => "on",
            "Certificate" => test_run("dc", "dc-leaf-client.crt"),
            "PrivateKey" => test_run("dc", "dc-leaf-client.key"),
            "DC" => test_run("dc", "dc-client.dc"),
            "DCKey" => test_run("dc", "dc-client.key"),
        },
        test => {
            "ExpectedResult" => "Success",
            "ExpextClientDCusage" => "VerifyPeerAndSignByDC",
            "ExpextServerDCusage" => "VerifyPeerAndSignByDC",
        },
    },
    {
        name => "test depoly cert and dc at the same time, but do not open dc ",
        server => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "Enable_sign_by_dc" => "off",
            "Enable_verify_peer_by_dc" => "off",
            "Certificate" => test_run("dc", "dc-leaf-server.crt"),
            "PrivateKey" => test_run("dc", "dc-leaf-server.key"),
            "DC" => test_run("dc", "dc-server.dc"),
            "DCKey" => test_run("dc", "dc-server.key"),
            "VerifyCAFile" => test_run("dc", "dc-chain-ca.crt"),
            "VerifyMode" => "Request",
        },
        client => {
            "MinProtocol" => "TLSv1.3",
            "MaxProtocol" => "TLSv1.3",
            "VerifyCAFile" => test_run("dc", "dc-chain-ca.crt"),
            "Enable_sign_by_dc" => "off",
            "Enable_verify_peer_by_dc" => "off",
            "Certificate" => test_run("dc", "dc-leaf-client.crt"),
            "PrivateKey" => test_run("dc", "dc-leaf-client.key"),
            "DC" => test_run("dc", "dc-client.dc"),
            "DCKey" => test_run("dc", "dc-client.key"),
        },
        test => {
            "ExpectedResult" => "Success",
            "ExpextClientDCusage" => "NotUseDC",
            "ExpextServerDCusage" => "NotUseDC",
        },
    },
);