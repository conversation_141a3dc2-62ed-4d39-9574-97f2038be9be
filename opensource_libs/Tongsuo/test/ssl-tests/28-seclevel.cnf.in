# -*- mode: perl; -*-
# Copyright 2016-2021 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## SSL test configurations

package ssltests;
use OpenSSL::Test::Utils;

our $fips_mode;

our @tests = (
    {
        name => "SECLEVEL 3 with default key",
        server => { "CipherString" => "DEFAULT:\@SECLEVEL=3" },
        client => { },
        test   => { "ExpectedResult" => "ServerFail" },
    },
);

our @tests_ec = (
    {
        name => "SECLEVEL 4 with ED448 key",
        server => { "CipherString" => "DEFAULT:\@SECLEVEL=4",
                    "Certificate" => test_pem("server-ed448-cert.pem"),
                    "PrivateKey" => test_pem("server-ed448-key.pem") },
        client => { "CipherString" => "DEFAULT:\@SECLEVEL=4",
                    "VerifyCAFile" => test_pem("root-ed448-cert.pem") },
        test   => { "ExpectedResult" => "Success" },
    },
    {
        # The Ed448 signature algorithm will not be enabled.
        # Because of the config order, the certificate is first loaded, and
        # then the security level is chaged. If you try this with s_server
        # the order will be reversed and it will instead fail to load the key.
        name => "SECLEVEL 5 server with ED448 key",
        server => { "CipherString" => "DEFAULT:\@SECLEVEL=5",
                    "Certificate" => test_pem("server-ed448-cert.pem"),
                    "PrivateKey" => test_pem("server-ed448-key.pem") },
        client => { "CipherString" => "DEFAULT:\@SECLEVEL=4",
                    "VerifyCAFile" => test_pem("root-ed448-cert.pem") },
        test   => { "ExpectedResult" => "ServerFail" },
    },
    {
        # The client will not sent the Ed448 signature algorithm, so the server
        # doesn't have a useable signature algorithm for the certificate.
        name => "SECLEVEL 5 client with ED448 key",
        server => { "CipherString" => "DEFAULT:\@SECLEVEL=4",
                    "Certificate" => test_pem("server-ed448-cert.pem"),
                    "PrivateKey" => test_pem("server-ed448-key.pem") },
        client => { "CipherString" => "DEFAULT:\@SECLEVEL=5",
                    "VerifyCAFile" => test_pem("root-ed448-cert.pem") },
        test   => { "ExpectedResult" => "ServerFail" },
    },
    {
        name => "SECLEVEL 3 with P-384 key, X25519 ECDHE",
        server => { "CipherString" => "DEFAULT:\@SECLEVEL=3",
                    "Certificate" => test_pem("p384-server-cert.pem"),
                    "PrivateKey" => test_pem("p384-server-key.pem"),
                    "Groups" => "X25519" },
        client => { "CipherString" => "ECDHE:\@SECLEVEL=3",
                    "VerifyCAFile" => test_pem("p384-root.pem") },
        test   => { "ExpectedResult" => "Success" },
    },
);

our @tests_tls1_2 = (
    {
        name => "SECLEVEL 3 with ED448 key, TLSv1.2",
        server => { "CipherString" => "DEFAULT:\@SECLEVEL=3",
                    "Certificate" => test_pem("server-ed448-cert.pem"),
                    "PrivateKey" => test_pem("server-ed448-key.pem"),
                    "MaxProtocol" => "TLSv1.2" },
        client => { "VerifyCAFile" => test_pem("root-ed448-cert.pem") },
        test   => { "ExpectedResult" => "Success" },
    },
);

push @tests, @tests_ec unless disabled("ec");
push @tests, @tests_tls1_2 unless disabled("tls1_2") || disabled("ec");
