# -*- mode: perl; -*-
# Copyright 2017-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html

use strict;
use warnings;

package ssltests;

# SRP is only supported up to TLSv1.2

our @tests = (
    {
        name => "srp",
        server => {
            "CipherString" => "SRP",
            "MaxProtocol" => "TLSv1.2",
            extra => {
                "SRPUser" => "user",
                "SRPPassword" => "password",
            },
        },
        client => {
            "CipherString" => "SRP",
            "MaxProtocol" => "TLSv1.2",
            extra => {
                "SRPUser" => "user",
                "SRPPassword" => "password",
            },
        },
        test => {
            "ExpectedResult" => "Success"
        },
    },
    {
        name => "srp-bad-password",
        server => {
            "CipherString" => "SRP",
            "MaxProtocol" => "TLSv1.2",
            extra => {
                "SRPUser" => "user",
                "SRPPassword" => "password",
            },
        },
        client => {
            "CipherString" => "SRP",
            "MaxProtocol" => "TLSv1.2",
            extra => {
                "SRPUser" => "user",
                "SRPPassword" => "passw0rd",
            },
        },
        test => {
            # Server fails first with bad client Finished.
            "ExpectedResult" => "ServerFail"
        },
    },
    {
        name => "srp-auth",
        server => {
            "CipherString" => "aSRP",
            "MaxProtocol" => "TLSv1.2",
            extra => {
                "SRPUser" => "user",
                "SRPPassword" => "password",
            },
        },
        client => {
            "CipherString" => "aSRP",
            "MaxProtocol" => "TLSv1.2",
            extra => {
                "SRPUser" => "user",
                "SRPPassword" => "password",
            },
        },
        test => {
            "ExpectedResult" => "Success"
        },
    },
    {
        name => "srp-auth-bad-password",
        server => {
            "CipherString" => "aSRP",
            "MaxProtocol" => "TLSv1.2",
            extra => {
                "SRPUser" => "user",
                "SRPPassword" => "password",
            },
        },
        client => {
            "CipherString" => "aSRP",
            "MaxProtocol" => "TLSv1.2",
            extra => {
                "SRPUser" => "user",
                "SRPPassword" => "passw0rd",
            },
        },
        test => {
            # Server fails first with bad client Finished.
            "ExpectedResult" => "ServerFail"
        },
    },
);
