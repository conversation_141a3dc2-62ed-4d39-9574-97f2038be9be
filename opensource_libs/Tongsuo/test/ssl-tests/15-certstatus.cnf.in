# -*- mode: perl; -*-
# Copyright 2016-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## Test CertStatus messages

use strict;
use warnings;

package ssltests;


our @tests = (
    {
        name => "certstatus-good",
        server => {
            extra => {
                "CertStatus" => "GoodResponse",
            },
        },
        client => {},
        test => {
            "Method" => "TLS",
            "ExpectedResult" => "Success"
        }
    },
    {
        name => "certstatus-bad",
        server => {
            extra => {
                "CertStatus" => "BadResponse",
            },
        },
        client => {},
        test => {
            "Method" => "TLS",
            "ExpectedResult" => "ClientFail"
        }
    },
);
