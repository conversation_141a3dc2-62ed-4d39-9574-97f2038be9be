# -*- mode: perl; -*-
# Copyright 2016-2020 The OpenSSL Project Authors. All Rights Reserved.
#
# Licensed under the Apache License 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://www.openssl.org/source/license.html


## Test packet fragmentation

use strict;
use warnings;

package ssltests;


our @tests = (
    # Default fragment size is 512.
    {
        name => "one-fragment-minus-app-data",
        server => { },
        client => { },
        test => {
            ApplicationData => 511,
        }
    },
    {
        name => "one-fragment-app-data",
        server => { },
        client => { },
        test => {
            ApplicationData => 512,
        }
    },
    {
        name => "one-fragment-plus-app-data",
        server => { },
        client => { },
        test => {
            ApplicationData => 513,
        }
    },
    {
        name => "small-app-data",
        server => { },
        client => { },
        test => {
            ApplicationData => 4 * 1024 + 1,
        }
    },
    {
        name => "small-app-data-large-fragment-size",
        server => { },
        client => { },
        test => {
            ApplicationData => 4 * 1024 + 1,
            MaxFragmentSize => 16384,
        }
    },
    {
        name => "medium-app-data",
        server => { },
        client => { },
        test => {
            ApplicationData => 32 * 1024 + 7,
        }
    },
    # Exceeds the 64kB write buffer size.
    {
        name => "medium-plus-app-data",
        server => { },
        client => { },
        test => {
            ApplicationData => 128 * 1024 - 3,
        }
    },
    {
        name => "large-app-data",
        server => { },
        client => { },
        test => {
            ApplicationData => 1024 * 1024,
        }
    },
    {
        name => "large-app-data-large-fragment-size",
        server => { },
        client => { },
        test => {
            ApplicationData => 1024 * 1024,
            MaxFragmentSize => 16384,
        }
    },
    {
        name => "large-app-data-odd-fragment-size",
        server => { },
        client => { },
        test => {
            ApplicationData => 1024 * 1024,
            MaxFragmentSize => 5 * 1024 - 5,
        }
    },
    # When the buffer / fragment size ratio is sufficiently large,
    # multi-buffer code kicks in on some platforms for AES-SHA.  The
    # exact minimum ratio depends on the platform, and is usually
    # around 4. Since the test buffer is 64kB, a 4kB fragment is
    # easily sufficient.
    #
    # (We run this test on all platforms though it's only true multibuffer
    #  on some of them.)
    {
        name => "large-app-data-aes-sha1-multibuffer",
        server => { },
        client => {
            CipherString => "AES128-SHA",
            MaxProtocol => "TLSv1.2"
        },
        test => {
            ApplicationData => 1024 * 1024,
            MaxFragmentSize => 4 * 1024,
        }
    },
    {
        name => "large-app-data-aes-sha2-multibuffer",
        server => { },
        client => {
            CipherString => "AES128-SHA256",
            MaxProtocol => "TLSv1.2"
        },
        test => {
            ApplicationData => 1024 * 1024,
            MaxFragmentSize => 4 * 1024,
        }
    },
        {
        name => "large-app-data-aes-sha1-multibuffer-odd-fragment",
        server => { },
        client => {
            CipherString => "AES128-SHA",
            MaxProtocol => "TLSv1.2"
        },
        test => {
            ApplicationData => 1024 * 1024 + 3,
            MaxFragmentSize => 5 * 1024 - 5,
        }
    },
    {
        name => "large-app-data-aes-sha2-multibuffer-odd-fragment",
        server => { },
        client => {
            CipherString => "AES128-SHA256",
            MaxProtocol => "TLSv1.2"
        },
        test => {
            ApplicationData => 1024 * 1024 - 3,
            MaxFragmentSize => 5 * 1024 + 5,
        }
    },
    # Test that multibuffer-capable code also handles small data correctly.
    # Here fragment size == app data size < buffer size,
    # so no multibuffering should happen.
    {
        name => "small-app-data-aes-sha1-multibuffer",
        server => { },
        client => {
            CipherString => "AES128-SHA",
            MaxProtocol => "TLSv1.2"
        },
        test => {
            ApplicationData => 4 * 1024,
            MaxFragmentSize => 4 * 1024,
        }
    },
    {
        name => "small-app-data-aes-sha2-multibuffer",
        server => { },
        client => {
            CipherString => "AES128-SHA256",
            MaxProtocol => "TLSv1.2"
        },
        test => {
            ApplicationData => 4 * 1024,
            MaxFragmentSize => 4 * 1024,
        }
    },
    ############################################
    # Default (Max) Fragment Size is 512.
    # Default Application data size is 256.
    {
        name => "Maximum Fragment Len extension set to 1024 w. FragmentSize disabled",
        server => { },
        client => {
            extra => {
                MaxFragmentLenExt => 1024,
            },
        },
        test => {
            ApplicationData => 3072,
            MaxFragmentSize => 16384,
        }
    },
    {
        name => "Maximum Fragment Len extension equal FragmentSize to 2048",
        server => { },
        client => {
            extra => {
                MaxFragmentLenExt => 2048,
            },
        },
        test => {
            ApplicationData => 3072,
            MaxFragmentSize => 2048,
        }
    },
    {
        name => "Maximum Fragment Len extension 512 lower than FragmentSize 1024",
        server => { },
        client => {
            extra => {
                MaxFragmentLenExt => 512,
            },
        },
        test => {
            ApplicationData => 3072,
            MaxFragmentSize => 1024,
        }
    },
    {
        name => "Maximum Fragment Len extension 1024 lower than FragmentSize 1024",
        server => { },
        client => {
            extra => {
                MaxFragmentLenExt => 2048,
            },
        },
        test => {
            ApplicationData => 3072,
            MaxFragmentSize => 1024,
        }
    },
    {
        name => "Maximum Fragment Len extension 4096 greater than FragmentSize 2048",
        server => { },
        client => {
            extra => {
                MaxFragmentLenExt => 4096,
            },
        },
        test => {
            ApplicationData => 8196,
            MaxFragmentSize => 2048,
        }
    },
    {
        name => "Maximum Fragment Len extension 2048 greater than FragmentSize 1024",
        server => { },
        client => {
            extra => {
                MaxFragmentLenExt => 2048,
            },
        },
        test => {
            ApplicationData => 3072,
            MaxFragmentSize => 1024,
        }
    },
);
