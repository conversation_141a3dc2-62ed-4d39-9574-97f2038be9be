# Generated with generate_ssl_tests.pl

num_tests = 678

test-0 = 0-version-negotiation
test-1 = 1-version-negotiation
test-2 = 2-version-negotiation
test-3 = 3-version-negotiation
test-4 = 4-version-negotiation
test-5 = 5-version-negotiation
test-6 = 6-version-negotiation
test-7 = 7-version-negotiation
test-8 = 8-version-negotiation
test-9 = 9-version-negotiation
test-10 = 10-version-negotiation
test-11 = 11-version-negotiation
test-12 = 12-version-negotiation
test-13 = 13-version-negotiation
test-14 = 14-version-negotiation
test-15 = 15-version-negotiation
test-16 = 16-version-negotiation
test-17 = 17-version-negotiation
test-18 = 18-version-negotiation
test-19 = 19-version-negotiation
test-20 = 20-version-negotiation
test-21 = 21-version-negotiation
test-22 = 22-version-negotiation
test-23 = 23-version-negotiation
test-24 = 24-version-negotiation
test-25 = 25-version-negotiation
test-26 = 26-version-negotiation
test-27 = 27-version-negotiation
test-28 = 28-version-negotiation
test-29 = 29-version-negotiation
test-30 = 30-version-negotiation
test-31 = 31-version-negotiation
test-32 = 32-version-negotiation
test-33 = 33-version-negotiation
test-34 = 34-version-negotiation
test-35 = 35-version-negotiation
test-36 = 36-version-negotiation
test-37 = 37-version-negotiation
test-38 = 38-version-negotiation
test-39 = 39-version-negotiation
test-40 = 40-version-negotiation
test-41 = 41-version-negotiation
test-42 = 42-version-negotiation
test-43 = 43-version-negotiation
test-44 = 44-version-negotiation
test-45 = 45-version-negotiation
test-46 = 46-version-negotiation
test-47 = 47-version-negotiation
test-48 = 48-version-negotiation
test-49 = 49-version-negotiation
test-50 = 50-version-negotiation
test-51 = 51-version-negotiation
test-52 = 52-version-negotiation
test-53 = 53-version-negotiation
test-54 = 54-version-negotiation
test-55 = 55-version-negotiation
test-56 = 56-version-negotiation
test-57 = 57-version-negotiation
test-58 = 58-version-negotiation
test-59 = 59-version-negotiation
test-60 = 60-version-negotiation
test-61 = 61-version-negotiation
test-62 = 62-version-negotiation
test-63 = 63-version-negotiation
test-64 = 64-version-negotiation
test-65 = 65-version-negotiation
test-66 = 66-version-negotiation
test-67 = 67-version-negotiation
test-68 = 68-version-negotiation
test-69 = 69-version-negotiation
test-70 = 70-version-negotiation
test-71 = 71-version-negotiation
test-72 = 72-version-negotiation
test-73 = 73-version-negotiation
test-74 = 74-version-negotiation
test-75 = 75-version-negotiation
test-76 = 76-version-negotiation
test-77 = 77-version-negotiation
test-78 = 78-version-negotiation
test-79 = 79-version-negotiation
test-80 = 80-version-negotiation
test-81 = 81-version-negotiation
test-82 = 82-version-negotiation
test-83 = 83-version-negotiation
test-84 = 84-version-negotiation
test-85 = 85-version-negotiation
test-86 = 86-version-negotiation
test-87 = 87-version-negotiation
test-88 = 88-version-negotiation
test-89 = 89-version-negotiation
test-90 = 90-version-negotiation
test-91 = 91-version-negotiation
test-92 = 92-version-negotiation
test-93 = 93-version-negotiation
test-94 = 94-version-negotiation
test-95 = 95-version-negotiation
test-96 = 96-version-negotiation
test-97 = 97-version-negotiation
test-98 = 98-version-negotiation
test-99 = 99-version-negotiation
test-100 = 100-version-negotiation
test-101 = 101-version-negotiation
test-102 = 102-version-negotiation
test-103 = 103-version-negotiation
test-104 = 104-version-negotiation
test-105 = 105-version-negotiation
test-106 = 106-version-negotiation
test-107 = 107-version-negotiation
test-108 = 108-version-negotiation
test-109 = 109-version-negotiation
test-110 = 110-version-negotiation
test-111 = 111-version-negotiation
test-112 = 112-version-negotiation
test-113 = 113-version-negotiation
test-114 = 114-version-negotiation
test-115 = 115-version-negotiation
test-116 = 116-version-negotiation
test-117 = 117-version-negotiation
test-118 = 118-version-negotiation
test-119 = 119-version-negotiation
test-120 = 120-version-negotiation
test-121 = 121-version-negotiation
test-122 = 122-version-negotiation
test-123 = 123-version-negotiation
test-124 = 124-version-negotiation
test-125 = 125-version-negotiation
test-126 = 126-version-negotiation
test-127 = 127-version-negotiation
test-128 = 128-version-negotiation
test-129 = 129-version-negotiation
test-130 = 130-version-negotiation
test-131 = 131-version-negotiation
test-132 = 132-version-negotiation
test-133 = 133-version-negotiation
test-134 = 134-version-negotiation
test-135 = 135-version-negotiation
test-136 = 136-version-negotiation
test-137 = 137-version-negotiation
test-138 = 138-version-negotiation
test-139 = 139-version-negotiation
test-140 = 140-version-negotiation
test-141 = 141-version-negotiation
test-142 = 142-version-negotiation
test-143 = 143-version-negotiation
test-144 = 144-version-negotiation
test-145 = 145-version-negotiation
test-146 = 146-version-negotiation
test-147 = 147-version-negotiation
test-148 = 148-version-negotiation
test-149 = 149-version-negotiation
test-150 = 150-version-negotiation
test-151 = 151-version-negotiation
test-152 = 152-version-negotiation
test-153 = 153-version-negotiation
test-154 = 154-version-negotiation
test-155 = 155-version-negotiation
test-156 = 156-version-negotiation
test-157 = 157-version-negotiation
test-158 = 158-version-negotiation
test-159 = 159-version-negotiation
test-160 = 160-version-negotiation
test-161 = 161-version-negotiation
test-162 = 162-version-negotiation
test-163 = 163-version-negotiation
test-164 = 164-version-negotiation
test-165 = 165-version-negotiation
test-166 = 166-version-negotiation
test-167 = 167-version-negotiation
test-168 = 168-version-negotiation
test-169 = 169-version-negotiation
test-170 = 170-version-negotiation
test-171 = 171-version-negotiation
test-172 = 172-version-negotiation
test-173 = 173-version-negotiation
test-174 = 174-version-negotiation
test-175 = 175-version-negotiation
test-176 = 176-version-negotiation
test-177 = 177-version-negotiation
test-178 = 178-version-negotiation
test-179 = 179-version-negotiation
test-180 = 180-version-negotiation
test-181 = 181-version-negotiation
test-182 = 182-version-negotiation
test-183 = 183-version-negotiation
test-184 = 184-version-negotiation
test-185 = 185-version-negotiation
test-186 = 186-version-negotiation
test-187 = 187-version-negotiation
test-188 = 188-version-negotiation
test-189 = 189-version-negotiation
test-190 = 190-version-negotiation
test-191 = 191-version-negotiation
test-192 = 192-version-negotiation
test-193 = 193-version-negotiation
test-194 = 194-version-negotiation
test-195 = 195-version-negotiation
test-196 = 196-version-negotiation
test-197 = 197-version-negotiation
test-198 = 198-version-negotiation
test-199 = 199-version-negotiation
test-200 = 200-version-negotiation
test-201 = 201-version-negotiation
test-202 = 202-version-negotiation
test-203 = 203-version-negotiation
test-204 = 204-version-negotiation
test-205 = 205-version-negotiation
test-206 = 206-version-negotiation
test-207 = 207-version-negotiation
test-208 = 208-version-negotiation
test-209 = 209-version-negotiation
test-210 = 210-version-negotiation
test-211 = 211-version-negotiation
test-212 = 212-version-negotiation
test-213 = 213-version-negotiation
test-214 = 214-version-negotiation
test-215 = 215-version-negotiation
test-216 = 216-version-negotiation
test-217 = 217-version-negotiation
test-218 = 218-version-negotiation
test-219 = 219-version-negotiation
test-220 = 220-version-negotiation
test-221 = 221-version-negotiation
test-222 = 222-version-negotiation
test-223 = 223-version-negotiation
test-224 = 224-version-negotiation
test-225 = 225-version-negotiation
test-226 = 226-version-negotiation
test-227 = 227-version-negotiation
test-228 = 228-version-negotiation
test-229 = 229-version-negotiation
test-230 = 230-version-negotiation
test-231 = 231-version-negotiation
test-232 = 232-version-negotiation
test-233 = 233-version-negotiation
test-234 = 234-version-negotiation
test-235 = 235-version-negotiation
test-236 = 236-version-negotiation
test-237 = 237-version-negotiation
test-238 = 238-version-negotiation
test-239 = 239-version-negotiation
test-240 = 240-version-negotiation
test-241 = 241-version-negotiation
test-242 = 242-version-negotiation
test-243 = 243-version-negotiation
test-244 = 244-version-negotiation
test-245 = 245-version-negotiation
test-246 = 246-version-negotiation
test-247 = 247-version-negotiation
test-248 = 248-version-negotiation
test-249 = 249-version-negotiation
test-250 = 250-version-negotiation
test-251 = 251-version-negotiation
test-252 = 252-version-negotiation
test-253 = 253-version-negotiation
test-254 = 254-version-negotiation
test-255 = 255-version-negotiation
test-256 = 256-version-negotiation
test-257 = 257-version-negotiation
test-258 = 258-version-negotiation
test-259 = 259-version-negotiation
test-260 = 260-version-negotiation
test-261 = 261-version-negotiation
test-262 = 262-version-negotiation
test-263 = 263-version-negotiation
test-264 = 264-version-negotiation
test-265 = 265-version-negotiation
test-266 = 266-version-negotiation
test-267 = 267-version-negotiation
test-268 = 268-version-negotiation
test-269 = 269-version-negotiation
test-270 = 270-version-negotiation
test-271 = 271-version-negotiation
test-272 = 272-version-negotiation
test-273 = 273-version-negotiation
test-274 = 274-version-negotiation
test-275 = 275-version-negotiation
test-276 = 276-version-negotiation
test-277 = 277-version-negotiation
test-278 = 278-version-negotiation
test-279 = 279-version-negotiation
test-280 = 280-version-negotiation
test-281 = 281-version-negotiation
test-282 = 282-version-negotiation
test-283 = 283-version-negotiation
test-284 = 284-version-negotiation
test-285 = 285-version-negotiation
test-286 = 286-version-negotiation
test-287 = 287-version-negotiation
test-288 = 288-version-negotiation
test-289 = 289-version-negotiation
test-290 = 290-version-negotiation
test-291 = 291-version-negotiation
test-292 = 292-version-negotiation
test-293 = 293-version-negotiation
test-294 = 294-version-negotiation
test-295 = 295-version-negotiation
test-296 = 296-version-negotiation
test-297 = 297-version-negotiation
test-298 = 298-version-negotiation
test-299 = 299-version-negotiation
test-300 = 300-version-negotiation
test-301 = 301-version-negotiation
test-302 = 302-version-negotiation
test-303 = 303-version-negotiation
test-304 = 304-version-negotiation
test-305 = 305-version-negotiation
test-306 = 306-version-negotiation
test-307 = 307-version-negotiation
test-308 = 308-version-negotiation
test-309 = 309-version-negotiation
test-310 = 310-version-negotiation
test-311 = 311-version-negotiation
test-312 = 312-version-negotiation
test-313 = 313-version-negotiation
test-314 = 314-version-negotiation
test-315 = 315-version-negotiation
test-316 = 316-version-negotiation
test-317 = 317-version-negotiation
test-318 = 318-version-negotiation
test-319 = 319-version-negotiation
test-320 = 320-version-negotiation
test-321 = 321-version-negotiation
test-322 = 322-version-negotiation
test-323 = 323-version-negotiation
test-324 = 324-version-negotiation
test-325 = 325-version-negotiation
test-326 = 326-version-negotiation
test-327 = 327-version-negotiation
test-328 = 328-version-negotiation
test-329 = 329-version-negotiation
test-330 = 330-version-negotiation
test-331 = 331-version-negotiation
test-332 = 332-version-negotiation
test-333 = 333-version-negotiation
test-334 = 334-version-negotiation
test-335 = 335-version-negotiation
test-336 = 336-version-negotiation
test-337 = 337-version-negotiation
test-338 = 338-version-negotiation
test-339 = 339-version-negotiation
test-340 = 340-version-negotiation
test-341 = 341-version-negotiation
test-342 = 342-version-negotiation
test-343 = 343-version-negotiation
test-344 = 344-version-negotiation
test-345 = 345-version-negotiation
test-346 = 346-version-negotiation
test-347 = 347-version-negotiation
test-348 = 348-version-negotiation
test-349 = 349-version-negotiation
test-350 = 350-version-negotiation
test-351 = 351-version-negotiation
test-352 = 352-version-negotiation
test-353 = 353-version-negotiation
test-354 = 354-version-negotiation
test-355 = 355-version-negotiation
test-356 = 356-version-negotiation
test-357 = 357-version-negotiation
test-358 = 358-version-negotiation
test-359 = 359-version-negotiation
test-360 = 360-version-negotiation
test-361 = 361-version-negotiation
test-362 = 362-version-negotiation
test-363 = 363-version-negotiation
test-364 = 364-version-negotiation
test-365 = 365-version-negotiation
test-366 = 366-version-negotiation
test-367 = 367-version-negotiation
test-368 = 368-version-negotiation
test-369 = 369-version-negotiation
test-370 = 370-version-negotiation
test-371 = 371-version-negotiation
test-372 = 372-version-negotiation
test-373 = 373-version-negotiation
test-374 = 374-version-negotiation
test-375 = 375-version-negotiation
test-376 = 376-version-negotiation
test-377 = 377-version-negotiation
test-378 = 378-version-negotiation
test-379 = 379-version-negotiation
test-380 = 380-version-negotiation
test-381 = 381-version-negotiation
test-382 = 382-version-negotiation
test-383 = 383-version-negotiation
test-384 = 384-version-negotiation
test-385 = 385-version-negotiation
test-386 = 386-version-negotiation
test-387 = 387-version-negotiation
test-388 = 388-version-negotiation
test-389 = 389-version-negotiation
test-390 = 390-version-negotiation
test-391 = 391-version-negotiation
test-392 = 392-version-negotiation
test-393 = 393-version-negotiation
test-394 = 394-version-negotiation
test-395 = 395-version-negotiation
test-396 = 396-version-negotiation
test-397 = 397-version-negotiation
test-398 = 398-version-negotiation
test-399 = 399-version-negotiation
test-400 = 400-version-negotiation
test-401 = 401-version-negotiation
test-402 = 402-version-negotiation
test-403 = 403-version-negotiation
test-404 = 404-version-negotiation
test-405 = 405-version-negotiation
test-406 = 406-version-negotiation
test-407 = 407-version-negotiation
test-408 = 408-version-negotiation
test-409 = 409-version-negotiation
test-410 = 410-version-negotiation
test-411 = 411-version-negotiation
test-412 = 412-version-negotiation
test-413 = 413-version-negotiation
test-414 = 414-version-negotiation
test-415 = 415-version-negotiation
test-416 = 416-version-negotiation
test-417 = 417-version-negotiation
test-418 = 418-version-negotiation
test-419 = 419-version-negotiation
test-420 = 420-version-negotiation
test-421 = 421-version-negotiation
test-422 = 422-version-negotiation
test-423 = 423-version-negotiation
test-424 = 424-version-negotiation
test-425 = 425-version-negotiation
test-426 = 426-version-negotiation
test-427 = 427-version-negotiation
test-428 = 428-version-negotiation
test-429 = 429-version-negotiation
test-430 = 430-version-negotiation
test-431 = 431-version-negotiation
test-432 = 432-version-negotiation
test-433 = 433-version-negotiation
test-434 = 434-version-negotiation
test-435 = 435-version-negotiation
test-436 = 436-version-negotiation
test-437 = 437-version-negotiation
test-438 = 438-version-negotiation
test-439 = 439-version-negotiation
test-440 = 440-version-negotiation
test-441 = 441-version-negotiation
test-442 = 442-version-negotiation
test-443 = 443-version-negotiation
test-444 = 444-version-negotiation
test-445 = 445-version-negotiation
test-446 = 446-version-negotiation
test-447 = 447-version-negotiation
test-448 = 448-version-negotiation
test-449 = 449-version-negotiation
test-450 = 450-version-negotiation
test-451 = 451-version-negotiation
test-452 = 452-version-negotiation
test-453 = 453-version-negotiation
test-454 = 454-version-negotiation
test-455 = 455-version-negotiation
test-456 = 456-version-negotiation
test-457 = 457-version-negotiation
test-458 = 458-version-negotiation
test-459 = 459-version-negotiation
test-460 = 460-version-negotiation
test-461 = 461-version-negotiation
test-462 = 462-version-negotiation
test-463 = 463-version-negotiation
test-464 = 464-version-negotiation
test-465 = 465-version-negotiation
test-466 = 466-version-negotiation
test-467 = 467-version-negotiation
test-468 = 468-version-negotiation
test-469 = 469-version-negotiation
test-470 = 470-version-negotiation
test-471 = 471-version-negotiation
test-472 = 472-version-negotiation
test-473 = 473-version-negotiation
test-474 = 474-version-negotiation
test-475 = 475-version-negotiation
test-476 = 476-version-negotiation
test-477 = 477-version-negotiation
test-478 = 478-version-negotiation
test-479 = 479-version-negotiation
test-480 = 480-version-negotiation
test-481 = 481-version-negotiation
test-482 = 482-version-negotiation
test-483 = 483-version-negotiation
test-484 = 484-version-negotiation
test-485 = 485-version-negotiation
test-486 = 486-version-negotiation
test-487 = 487-version-negotiation
test-488 = 488-version-negotiation
test-489 = 489-version-negotiation
test-490 = 490-version-negotiation
test-491 = 491-version-negotiation
test-492 = 492-version-negotiation
test-493 = 493-version-negotiation
test-494 = 494-version-negotiation
test-495 = 495-version-negotiation
test-496 = 496-version-negotiation
test-497 = 497-version-negotiation
test-498 = 498-version-negotiation
test-499 = 499-version-negotiation
test-500 = 500-version-negotiation
test-501 = 501-version-negotiation
test-502 = 502-version-negotiation
test-503 = 503-version-negotiation
test-504 = 504-version-negotiation
test-505 = 505-version-negotiation
test-506 = 506-version-negotiation
test-507 = 507-version-negotiation
test-508 = 508-version-negotiation
test-509 = 509-version-negotiation
test-510 = 510-version-negotiation
test-511 = 511-version-negotiation
test-512 = 512-version-negotiation
test-513 = 513-version-negotiation
test-514 = 514-version-negotiation
test-515 = 515-version-negotiation
test-516 = 516-version-negotiation
test-517 = 517-version-negotiation
test-518 = 518-version-negotiation
test-519 = 519-version-negotiation
test-520 = 520-version-negotiation
test-521 = 521-version-negotiation
test-522 = 522-version-negotiation
test-523 = 523-version-negotiation
test-524 = 524-version-negotiation
test-525 = 525-version-negotiation
test-526 = 526-version-negotiation
test-527 = 527-version-negotiation
test-528 = 528-version-negotiation
test-529 = 529-version-negotiation
test-530 = 530-version-negotiation
test-531 = 531-version-negotiation
test-532 = 532-version-negotiation
test-533 = 533-version-negotiation
test-534 = 534-version-negotiation
test-535 = 535-version-negotiation
test-536 = 536-version-negotiation
test-537 = 537-version-negotiation
test-538 = 538-version-negotiation
test-539 = 539-version-negotiation
test-540 = 540-version-negotiation
test-541 = 541-version-negotiation
test-542 = 542-version-negotiation
test-543 = 543-version-negotiation
test-544 = 544-version-negotiation
test-545 = 545-version-negotiation
test-546 = 546-version-negotiation
test-547 = 547-version-negotiation
test-548 = 548-version-negotiation
test-549 = 549-version-negotiation
test-550 = 550-version-negotiation
test-551 = 551-version-negotiation
test-552 = 552-version-negotiation
test-553 = 553-version-negotiation
test-554 = 554-version-negotiation
test-555 = 555-version-negotiation
test-556 = 556-version-negotiation
test-557 = 557-version-negotiation
test-558 = 558-version-negotiation
test-559 = 559-version-negotiation
test-560 = 560-version-negotiation
test-561 = 561-version-negotiation
test-562 = 562-version-negotiation
test-563 = 563-version-negotiation
test-564 = 564-version-negotiation
test-565 = 565-version-negotiation
test-566 = 566-version-negotiation
test-567 = 567-version-negotiation
test-568 = 568-version-negotiation
test-569 = 569-version-negotiation
test-570 = 570-version-negotiation
test-571 = 571-version-negotiation
test-572 = 572-version-negotiation
test-573 = 573-version-negotiation
test-574 = 574-version-negotiation
test-575 = 575-version-negotiation
test-576 = 576-version-negotiation
test-577 = 577-version-negotiation
test-578 = 578-version-negotiation
test-579 = 579-version-negotiation
test-580 = 580-version-negotiation
test-581 = 581-version-negotiation
test-582 = 582-version-negotiation
test-583 = 583-version-negotiation
test-584 = 584-version-negotiation
test-585 = 585-version-negotiation
test-586 = 586-version-negotiation
test-587 = 587-version-negotiation
test-588 = 588-version-negotiation
test-589 = 589-version-negotiation
test-590 = 590-version-negotiation
test-591 = 591-version-negotiation
test-592 = 592-version-negotiation
test-593 = 593-version-negotiation
test-594 = 594-version-negotiation
test-595 = 595-version-negotiation
test-596 = 596-version-negotiation
test-597 = 597-version-negotiation
test-598 = 598-version-negotiation
test-599 = 599-version-negotiation
test-600 = 600-version-negotiation
test-601 = 601-version-negotiation
test-602 = 602-version-negotiation
test-603 = 603-version-negotiation
test-604 = 604-version-negotiation
test-605 = 605-version-negotiation
test-606 = 606-version-negotiation
test-607 = 607-version-negotiation
test-608 = 608-version-negotiation
test-609 = 609-version-negotiation
test-610 = 610-version-negotiation
test-611 = 611-version-negotiation
test-612 = 612-version-negotiation
test-613 = 613-version-negotiation
test-614 = 614-version-negotiation
test-615 = 615-version-negotiation
test-616 = 616-version-negotiation
test-617 = 617-version-negotiation
test-618 = 618-version-negotiation
test-619 = 619-version-negotiation
test-620 = 620-version-negotiation
test-621 = 621-version-negotiation
test-622 = 622-version-negotiation
test-623 = 623-version-negotiation
test-624 = 624-version-negotiation
test-625 = 625-version-negotiation
test-626 = 626-version-negotiation
test-627 = 627-version-negotiation
test-628 = 628-version-negotiation
test-629 = 629-version-negotiation
test-630 = 630-version-negotiation
test-631 = 631-version-negotiation
test-632 = 632-version-negotiation
test-633 = 633-version-negotiation
test-634 = 634-version-negotiation
test-635 = 635-version-negotiation
test-636 = 636-version-negotiation
test-637 = 637-version-negotiation
test-638 = 638-version-negotiation
test-639 = 639-version-negotiation
test-640 = 640-version-negotiation
test-641 = 641-version-negotiation
test-642 = 642-version-negotiation
test-643 = 643-version-negotiation
test-644 = 644-version-negotiation
test-645 = 645-version-negotiation
test-646 = 646-version-negotiation
test-647 = 647-version-negotiation
test-648 = 648-version-negotiation
test-649 = 649-version-negotiation
test-650 = 650-version-negotiation
test-651 = 651-version-negotiation
test-652 = 652-version-negotiation
test-653 = 653-version-negotiation
test-654 = 654-version-negotiation
test-655 = 655-version-negotiation
test-656 = 656-version-negotiation
test-657 = 657-version-negotiation
test-658 = 658-version-negotiation
test-659 = 659-version-negotiation
test-660 = 660-version-negotiation
test-661 = 661-version-negotiation
test-662 = 662-version-negotiation
test-663 = 663-version-negotiation
test-664 = 664-version-negotiation
test-665 = 665-version-negotiation
test-666 = 666-version-negotiation
test-667 = 667-version-negotiation
test-668 = 668-version-negotiation
test-669 = 669-version-negotiation
test-670 = 670-version-negotiation
test-671 = 671-version-negotiation
test-672 = 672-version-negotiation
test-673 = 673-version-negotiation
test-674 = 674-version-negotiation
test-675 = 675-version-negotiation
test-676 = 676-ciphersuite-sanity-check-client
test-677 = 677-ciphersuite-sanity-check-server
# ===========================================================

[0-version-negotiation]
ssl_conf = 0-version-negotiation-ssl

[0-version-negotiation-ssl]
server = 0-version-negotiation-server
client = 0-version-negotiation-client

[0-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[0-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-0]
ExpectedResult = ClientFail


# ===========================================================

[1-version-negotiation]
ssl_conf = 1-version-negotiation-ssl

[1-version-negotiation-ssl]
server = 1-version-negotiation-server
client = 1-version-negotiation-client

[1-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[1-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-1]
ExpectedResult = ClientFail


# ===========================================================

[2-version-negotiation]
ssl_conf = 2-version-negotiation-ssl

[2-version-negotiation-ssl]
server = 2-version-negotiation-server
client = 2-version-negotiation-client

[2-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[2-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-2]
ExpectedResult = ClientFail


# ===========================================================

[3-version-negotiation]
ssl_conf = 3-version-negotiation-ssl

[3-version-negotiation-ssl]
server = 3-version-negotiation-server
client = 3-version-negotiation-client

[3-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[3-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-3]
ExpectedResult = ClientFail


# ===========================================================

[4-version-negotiation]
ssl_conf = 4-version-negotiation-ssl

[4-version-negotiation-ssl]
server = 4-version-negotiation-server
client = 4-version-negotiation-client

[4-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[4-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-4]
ExpectedResult = ClientFail


# ===========================================================

[5-version-negotiation]
ssl_conf = 5-version-negotiation-ssl

[5-version-negotiation-ssl]
server = 5-version-negotiation-server
client = 5-version-negotiation-client

[5-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[5-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-5]
ExpectedResult = ClientFail


# ===========================================================

[6-version-negotiation]
ssl_conf = 6-version-negotiation-ssl

[6-version-negotiation-ssl]
server = 6-version-negotiation-server
client = 6-version-negotiation-client

[6-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[6-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-6]
ExpectedResult = ClientFail


# ===========================================================

[7-version-negotiation]
ssl_conf = 7-version-negotiation-ssl

[7-version-negotiation-ssl]
server = 7-version-negotiation-server
client = 7-version-negotiation-client

[7-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[7-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-7]
ExpectedResult = ClientFail


# ===========================================================

[8-version-negotiation]
ssl_conf = 8-version-negotiation-ssl

[8-version-negotiation-ssl]
server = 8-version-negotiation-server
client = 8-version-negotiation-client

[8-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[8-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-8]
ExpectedResult = ClientFail


# ===========================================================

[9-version-negotiation]
ssl_conf = 9-version-negotiation-ssl

[9-version-negotiation-ssl]
server = 9-version-negotiation-server
client = 9-version-negotiation-client

[9-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[9-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-9]
ExpectedResult = ClientFail


# ===========================================================

[10-version-negotiation]
ssl_conf = 10-version-negotiation-ssl

[10-version-negotiation-ssl]
server = 10-version-negotiation-server
client = 10-version-negotiation-client

[10-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[10-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-10]
ExpectedResult = ClientFail


# ===========================================================

[11-version-negotiation]
ssl_conf = 11-version-negotiation-ssl

[11-version-negotiation-ssl]
server = 11-version-negotiation-server
client = 11-version-negotiation-client

[11-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[11-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-11]
ExpectedResult = ClientFail


# ===========================================================

[12-version-negotiation]
ssl_conf = 12-version-negotiation-ssl

[12-version-negotiation-ssl]
server = 12-version-negotiation-server
client = 12-version-negotiation-client

[12-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[12-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-12]
ExpectedResult = ClientFail


# ===========================================================

[13-version-negotiation]
ssl_conf = 13-version-negotiation-ssl

[13-version-negotiation-ssl]
server = 13-version-negotiation-server
client = 13-version-negotiation-client

[13-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[13-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-13]
ExpectedResult = ClientFail


# ===========================================================

[14-version-negotiation]
ssl_conf = 14-version-negotiation-ssl

[14-version-negotiation-ssl]
server = 14-version-negotiation-server
client = 14-version-negotiation-client

[14-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[14-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-14]
ExpectedResult = ClientFail


# ===========================================================

[15-version-negotiation]
ssl_conf = 15-version-negotiation-ssl

[15-version-negotiation-ssl]
server = 15-version-negotiation-server
client = 15-version-negotiation-client

[15-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[15-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-15]
ExpectedResult = ClientFail


# ===========================================================

[16-version-negotiation]
ssl_conf = 16-version-negotiation-ssl

[16-version-negotiation-ssl]
server = 16-version-negotiation-server
client = 16-version-negotiation-client

[16-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[16-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-16]
ExpectedResult = ClientFail


# ===========================================================

[17-version-negotiation]
ssl_conf = 17-version-negotiation-ssl

[17-version-negotiation-ssl]
server = 17-version-negotiation-server
client = 17-version-negotiation-client

[17-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[17-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-17]
ExpectedResult = ClientFail


# ===========================================================

[18-version-negotiation]
ssl_conf = 18-version-negotiation-ssl

[18-version-negotiation-ssl]
server = 18-version-negotiation-server
client = 18-version-negotiation-client

[18-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[18-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-18]
ExpectedResult = ClientFail


# ===========================================================

[19-version-negotiation]
ssl_conf = 19-version-negotiation-ssl

[19-version-negotiation-ssl]
server = 19-version-negotiation-server
client = 19-version-negotiation-client

[19-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[19-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-19]
ExpectedResult = ClientFail


# ===========================================================

[20-version-negotiation]
ssl_conf = 20-version-negotiation-ssl

[20-version-negotiation-ssl]
server = 20-version-negotiation-server
client = 20-version-negotiation-client

[20-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[20-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-20]
ExpectedResult = ClientFail


# ===========================================================

[21-version-negotiation]
ssl_conf = 21-version-negotiation-ssl

[21-version-negotiation-ssl]
server = 21-version-negotiation-server
client = 21-version-negotiation-client

[21-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[21-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-21]
ExpectedResult = ClientFail


# ===========================================================

[22-version-negotiation]
ssl_conf = 22-version-negotiation-ssl

[22-version-negotiation-ssl]
server = 22-version-negotiation-server
client = 22-version-negotiation-client

[22-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[22-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-22]
ExpectedResult = ClientFail


# ===========================================================

[23-version-negotiation]
ssl_conf = 23-version-negotiation-ssl

[23-version-negotiation-ssl]
server = 23-version-negotiation-server
client = 23-version-negotiation-client

[23-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[23-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-23]
ExpectedResult = ClientFail


# ===========================================================

[24-version-negotiation]
ssl_conf = 24-version-negotiation-ssl

[24-version-negotiation-ssl]
server = 24-version-negotiation-server
client = 24-version-negotiation-client

[24-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[24-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-24]
ExpectedResult = ClientFail


# ===========================================================

[25-version-negotiation]
ssl_conf = 25-version-negotiation-ssl

[25-version-negotiation-ssl]
server = 25-version-negotiation-server
client = 25-version-negotiation-client

[25-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[25-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-25]
ExpectedResult = ClientFail


# ===========================================================

[26-version-negotiation]
ssl_conf = 26-version-negotiation-ssl

[26-version-negotiation-ssl]
server = 26-version-negotiation-server
client = 26-version-negotiation-client

[26-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[26-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-26]
ExpectedResult = ServerFail


# ===========================================================

[27-version-negotiation]
ssl_conf = 27-version-negotiation-ssl

[27-version-negotiation-ssl]
server = 27-version-negotiation-server
client = 27-version-negotiation-client

[27-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[27-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-27]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[28-version-negotiation]
ssl_conf = 28-version-negotiation-ssl

[28-version-negotiation-ssl]
server = 28-version-negotiation-server
client = 28-version-negotiation-client

[28-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[28-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-28]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[29-version-negotiation]
ssl_conf = 29-version-negotiation-ssl

[29-version-negotiation-ssl]
server = 29-version-negotiation-server
client = 29-version-negotiation-client

[29-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[29-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-29]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[30-version-negotiation]
ssl_conf = 30-version-negotiation-ssl

[30-version-negotiation-ssl]
server = 30-version-negotiation-server
client = 30-version-negotiation-client

[30-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[30-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-30]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[31-version-negotiation]
ssl_conf = 31-version-negotiation-ssl

[31-version-negotiation-ssl]
server = 31-version-negotiation-server
client = 31-version-negotiation-client

[31-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[31-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-31]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[32-version-negotiation]
ssl_conf = 32-version-negotiation-ssl

[32-version-negotiation-ssl]
server = 32-version-negotiation-server
client = 32-version-negotiation-client

[32-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[32-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-32]
ExpectedResult = ServerFail


# ===========================================================

[33-version-negotiation]
ssl_conf = 33-version-negotiation-ssl

[33-version-negotiation-ssl]
server = 33-version-negotiation-server
client = 33-version-negotiation-client

[33-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[33-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-33]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[34-version-negotiation]
ssl_conf = 34-version-negotiation-ssl

[34-version-negotiation-ssl]
server = 34-version-negotiation-server
client = 34-version-negotiation-client

[34-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[34-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-34]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[35-version-negotiation]
ssl_conf = 35-version-negotiation-ssl

[35-version-negotiation-ssl]
server = 35-version-negotiation-server
client = 35-version-negotiation-client

[35-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[35-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-35]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[36-version-negotiation]
ssl_conf = 36-version-negotiation-ssl

[36-version-negotiation-ssl]
server = 36-version-negotiation-server
client = 36-version-negotiation-client

[36-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[36-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-36]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[37-version-negotiation]
ssl_conf = 37-version-negotiation-ssl

[37-version-negotiation-ssl]
server = 37-version-negotiation-server
client = 37-version-negotiation-client

[37-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[37-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-37]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[38-version-negotiation]
ssl_conf = 38-version-negotiation-ssl

[38-version-negotiation-ssl]
server = 38-version-negotiation-server
client = 38-version-negotiation-client

[38-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[38-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-38]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[39-version-negotiation]
ssl_conf = 39-version-negotiation-ssl

[39-version-negotiation-ssl]
server = 39-version-negotiation-server
client = 39-version-negotiation-client

[39-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[39-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-39]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[40-version-negotiation]
ssl_conf = 40-version-negotiation-ssl

[40-version-negotiation-ssl]
server = 40-version-negotiation-server
client = 40-version-negotiation-client

[40-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[40-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-40]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[41-version-negotiation]
ssl_conf = 41-version-negotiation-ssl

[41-version-negotiation-ssl]
server = 41-version-negotiation-server
client = 41-version-negotiation-client

[41-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[41-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-41]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[42-version-negotiation]
ssl_conf = 42-version-negotiation-ssl

[42-version-negotiation-ssl]
server = 42-version-negotiation-server
client = 42-version-negotiation-client

[42-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[42-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-42]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[43-version-negotiation]
ssl_conf = 43-version-negotiation-ssl

[43-version-negotiation-ssl]
server = 43-version-negotiation-server
client = 43-version-negotiation-client

[43-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[43-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-43]
ExpectedResult = ServerFail


# ===========================================================

[44-version-negotiation]
ssl_conf = 44-version-negotiation-ssl

[44-version-negotiation-ssl]
server = 44-version-negotiation-server
client = 44-version-negotiation-client

[44-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[44-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-44]
ExpectedResult = ServerFail


# ===========================================================

[45-version-negotiation]
ssl_conf = 45-version-negotiation-ssl

[45-version-negotiation-ssl]
server = 45-version-negotiation-server
client = 45-version-negotiation-client

[45-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[45-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-45]
ExpectedResult = ServerFail


# ===========================================================

[46-version-negotiation]
ssl_conf = 46-version-negotiation-ssl

[46-version-negotiation-ssl]
server = 46-version-negotiation-server
client = 46-version-negotiation-client

[46-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[46-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-46]
ExpectedResult = ServerFail


# ===========================================================

[47-version-negotiation]
ssl_conf = 47-version-negotiation-ssl

[47-version-negotiation-ssl]
server = 47-version-negotiation-server
client = 47-version-negotiation-client

[47-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[47-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-47]
ExpectedResult = ServerFail


# ===========================================================

[48-version-negotiation]
ssl_conf = 48-version-negotiation-ssl

[48-version-negotiation-ssl]
server = 48-version-negotiation-server
client = 48-version-negotiation-client

[48-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[48-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-48]
ExpectedResult = ServerFail


# ===========================================================

[49-version-negotiation]
ssl_conf = 49-version-negotiation-ssl

[49-version-negotiation-ssl]
server = 49-version-negotiation-server
client = 49-version-negotiation-client

[49-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[49-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-49]
ExpectedResult = ServerFail


# ===========================================================

[50-version-negotiation]
ssl_conf = 50-version-negotiation-ssl

[50-version-negotiation-ssl]
server = 50-version-negotiation-server
client = 50-version-negotiation-client

[50-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[50-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-50]
ExpectedResult = ServerFail


# ===========================================================

[51-version-negotiation]
ssl_conf = 51-version-negotiation-ssl

[51-version-negotiation-ssl]
server = 51-version-negotiation-server
client = 51-version-negotiation-client

[51-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[51-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-51]
ExpectedResult = ServerFail


# ===========================================================

[52-version-negotiation]
ssl_conf = 52-version-negotiation-ssl

[52-version-negotiation-ssl]
server = 52-version-negotiation-server
client = 52-version-negotiation-client

[52-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[52-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-52]
ExpectedResult = ServerFail


# ===========================================================

[53-version-negotiation]
ssl_conf = 53-version-negotiation-ssl

[53-version-negotiation-ssl]
server = 53-version-negotiation-server
client = 53-version-negotiation-client

[53-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[53-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-53]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[54-version-negotiation]
ssl_conf = 54-version-negotiation-ssl

[54-version-negotiation-ssl]
server = 54-version-negotiation-server
client = 54-version-negotiation-client

[54-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[54-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-54]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[55-version-negotiation]
ssl_conf = 55-version-negotiation-ssl

[55-version-negotiation-ssl]
server = 55-version-negotiation-server
client = 55-version-negotiation-client

[55-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[55-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-55]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[56-version-negotiation]
ssl_conf = 56-version-negotiation-ssl

[56-version-negotiation-ssl]
server = 56-version-negotiation-server
client = 56-version-negotiation-client

[56-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[56-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-56]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[57-version-negotiation]
ssl_conf = 57-version-negotiation-ssl

[57-version-negotiation-ssl]
server = 57-version-negotiation-server
client = 57-version-negotiation-client

[57-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[57-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-57]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[58-version-negotiation]
ssl_conf = 58-version-negotiation-ssl

[58-version-negotiation-ssl]
server = 58-version-negotiation-server
client = 58-version-negotiation-client

[58-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[58-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-58]
ExpectedResult = ServerFail


# ===========================================================

[59-version-negotiation]
ssl_conf = 59-version-negotiation-ssl

[59-version-negotiation-ssl]
server = 59-version-negotiation-server
client = 59-version-negotiation-client

[59-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[59-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-59]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[60-version-negotiation]
ssl_conf = 60-version-negotiation-ssl

[60-version-negotiation-ssl]
server = 60-version-negotiation-server
client = 60-version-negotiation-client

[60-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[60-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-60]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[61-version-negotiation]
ssl_conf = 61-version-negotiation-ssl

[61-version-negotiation-ssl]
server = 61-version-negotiation-server
client = 61-version-negotiation-client

[61-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[61-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-61]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[62-version-negotiation]
ssl_conf = 62-version-negotiation-ssl

[62-version-negotiation-ssl]
server = 62-version-negotiation-server
client = 62-version-negotiation-client

[62-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[62-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-62]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[63-version-negotiation]
ssl_conf = 63-version-negotiation-ssl

[63-version-negotiation-ssl]
server = 63-version-negotiation-server
client = 63-version-negotiation-client

[63-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[63-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-63]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[64-version-negotiation]
ssl_conf = 64-version-negotiation-ssl

[64-version-negotiation-ssl]
server = 64-version-negotiation-server
client = 64-version-negotiation-client

[64-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[64-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-64]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[65-version-negotiation]
ssl_conf = 65-version-negotiation-ssl

[65-version-negotiation-ssl]
server = 65-version-negotiation-server
client = 65-version-negotiation-client

[65-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[65-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-65]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[66-version-negotiation]
ssl_conf = 66-version-negotiation-ssl

[66-version-negotiation-ssl]
server = 66-version-negotiation-server
client = 66-version-negotiation-client

[66-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[66-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-66]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[67-version-negotiation]
ssl_conf = 67-version-negotiation-ssl

[67-version-negotiation-ssl]
server = 67-version-negotiation-server
client = 67-version-negotiation-client

[67-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[67-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-67]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[68-version-negotiation]
ssl_conf = 68-version-negotiation-ssl

[68-version-negotiation-ssl]
server = 68-version-negotiation-server
client = 68-version-negotiation-client

[68-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[68-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-68]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[69-version-negotiation]
ssl_conf = 69-version-negotiation-ssl

[69-version-negotiation-ssl]
server = 69-version-negotiation-server
client = 69-version-negotiation-client

[69-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[69-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-69]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[70-version-negotiation]
ssl_conf = 70-version-negotiation-ssl

[70-version-negotiation-ssl]
server = 70-version-negotiation-server
client = 70-version-negotiation-client

[70-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[70-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-70]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[71-version-negotiation]
ssl_conf = 71-version-negotiation-ssl

[71-version-negotiation-ssl]
server = 71-version-negotiation-server
client = 71-version-negotiation-client

[71-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[71-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-71]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[72-version-negotiation]
ssl_conf = 72-version-negotiation-ssl

[72-version-negotiation-ssl]
server = 72-version-negotiation-server
client = 72-version-negotiation-client

[72-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[72-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-72]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[73-version-negotiation]
ssl_conf = 73-version-negotiation-ssl

[73-version-negotiation-ssl]
server = 73-version-negotiation-server
client = 73-version-negotiation-client

[73-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[73-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-73]
ExpectedResult = ServerFail


# ===========================================================

[74-version-negotiation]
ssl_conf = 74-version-negotiation-ssl

[74-version-negotiation-ssl]
server = 74-version-negotiation-server
client = 74-version-negotiation-client

[74-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[74-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-74]
ExpectedResult = ServerFail


# ===========================================================

[75-version-negotiation]
ssl_conf = 75-version-negotiation-ssl

[75-version-negotiation-ssl]
server = 75-version-negotiation-server
client = 75-version-negotiation-client

[75-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[75-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-75]
ExpectedResult = ServerFail


# ===========================================================

[76-version-negotiation]
ssl_conf = 76-version-negotiation-ssl

[76-version-negotiation-ssl]
server = 76-version-negotiation-server
client = 76-version-negotiation-client

[76-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[76-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-76]
ExpectedResult = ServerFail


# ===========================================================

[77-version-negotiation]
ssl_conf = 77-version-negotiation-ssl

[77-version-negotiation-ssl]
server = 77-version-negotiation-server
client = 77-version-negotiation-client

[77-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[77-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-77]
ExpectedResult = ServerFail


# ===========================================================

[78-version-negotiation]
ssl_conf = 78-version-negotiation-ssl

[78-version-negotiation-ssl]
server = 78-version-negotiation-server
client = 78-version-negotiation-client

[78-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[78-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-78]
ExpectedResult = ServerFail


# ===========================================================

[79-version-negotiation]
ssl_conf = 79-version-negotiation-ssl

[79-version-negotiation-ssl]
server = 79-version-negotiation-server
client = 79-version-negotiation-client

[79-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[79-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-79]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[80-version-negotiation]
ssl_conf = 80-version-negotiation-ssl

[80-version-negotiation-ssl]
server = 80-version-negotiation-server
client = 80-version-negotiation-client

[80-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[80-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-80]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[81-version-negotiation]
ssl_conf = 81-version-negotiation-ssl

[81-version-negotiation-ssl]
server = 81-version-negotiation-server
client = 81-version-negotiation-client

[81-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[81-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-81]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[82-version-negotiation]
ssl_conf = 82-version-negotiation-ssl

[82-version-negotiation-ssl]
server = 82-version-negotiation-server
client = 82-version-negotiation-client

[82-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[82-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-82]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[83-version-negotiation]
ssl_conf = 83-version-negotiation-ssl

[83-version-negotiation-ssl]
server = 83-version-negotiation-server
client = 83-version-negotiation-client

[83-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[83-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-83]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[84-version-negotiation]
ssl_conf = 84-version-negotiation-ssl

[84-version-negotiation-ssl]
server = 84-version-negotiation-server
client = 84-version-negotiation-client

[84-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[84-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-84]
ExpectedResult = ServerFail


# ===========================================================

[85-version-negotiation]
ssl_conf = 85-version-negotiation-ssl

[85-version-negotiation-ssl]
server = 85-version-negotiation-server
client = 85-version-negotiation-client

[85-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[85-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-85]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[86-version-negotiation]
ssl_conf = 86-version-negotiation-ssl

[86-version-negotiation-ssl]
server = 86-version-negotiation-server
client = 86-version-negotiation-client

[86-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[86-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-86]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[87-version-negotiation]
ssl_conf = 87-version-negotiation-ssl

[87-version-negotiation-ssl]
server = 87-version-negotiation-server
client = 87-version-negotiation-client

[87-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[87-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-87]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[88-version-negotiation]
ssl_conf = 88-version-negotiation-ssl

[88-version-negotiation-ssl]
server = 88-version-negotiation-server
client = 88-version-negotiation-client

[88-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[88-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-88]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[89-version-negotiation]
ssl_conf = 89-version-negotiation-ssl

[89-version-negotiation-ssl]
server = 89-version-negotiation-server
client = 89-version-negotiation-client

[89-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[89-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-89]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[90-version-negotiation]
ssl_conf = 90-version-negotiation-ssl

[90-version-negotiation-ssl]
server = 90-version-negotiation-server
client = 90-version-negotiation-client

[90-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[90-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-90]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[91-version-negotiation]
ssl_conf = 91-version-negotiation-ssl

[91-version-negotiation-ssl]
server = 91-version-negotiation-server
client = 91-version-negotiation-client

[91-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[91-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-91]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[92-version-negotiation]
ssl_conf = 92-version-negotiation-ssl

[92-version-negotiation-ssl]
server = 92-version-negotiation-server
client = 92-version-negotiation-client

[92-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[92-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-92]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[93-version-negotiation]
ssl_conf = 93-version-negotiation-ssl

[93-version-negotiation-ssl]
server = 93-version-negotiation-server
client = 93-version-negotiation-client

[93-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[93-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-93]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[94-version-negotiation]
ssl_conf = 94-version-negotiation-ssl

[94-version-negotiation-ssl]
server = 94-version-negotiation-server
client = 94-version-negotiation-client

[94-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[94-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-94]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[95-version-negotiation]
ssl_conf = 95-version-negotiation-ssl

[95-version-negotiation-ssl]
server = 95-version-negotiation-server
client = 95-version-negotiation-client

[95-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[95-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-95]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[96-version-negotiation]
ssl_conf = 96-version-negotiation-ssl

[96-version-negotiation-ssl]
server = 96-version-negotiation-server
client = 96-version-negotiation-client

[96-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[96-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-96]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[97-version-negotiation]
ssl_conf = 97-version-negotiation-ssl

[97-version-negotiation-ssl]
server = 97-version-negotiation-server
client = 97-version-negotiation-client

[97-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[97-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-97]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[98-version-negotiation]
ssl_conf = 98-version-negotiation-ssl

[98-version-negotiation-ssl]
server = 98-version-negotiation-server
client = 98-version-negotiation-client

[98-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[98-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-98]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[99-version-negotiation]
ssl_conf = 99-version-negotiation-ssl

[99-version-negotiation-ssl]
server = 99-version-negotiation-server
client = 99-version-negotiation-client

[99-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[99-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-99]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[100-version-negotiation]
ssl_conf = 100-version-negotiation-ssl

[100-version-negotiation-ssl]
server = 100-version-negotiation-server
client = 100-version-negotiation-client

[100-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[100-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-100]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[101-version-negotiation]
ssl_conf = 101-version-negotiation-ssl

[101-version-negotiation-ssl]
server = 101-version-negotiation-server
client = 101-version-negotiation-client

[101-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[101-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-101]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[102-version-negotiation]
ssl_conf = 102-version-negotiation-ssl

[102-version-negotiation-ssl]
server = 102-version-negotiation-server
client = 102-version-negotiation-client

[102-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[102-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-102]
ExpectedResult = ServerFail


# ===========================================================

[103-version-negotiation]
ssl_conf = 103-version-negotiation-ssl

[103-version-negotiation-ssl]
server = 103-version-negotiation-server
client = 103-version-negotiation-client

[103-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[103-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-103]
ExpectedResult = ServerFail


# ===========================================================

[104-version-negotiation]
ssl_conf = 104-version-negotiation-ssl

[104-version-negotiation-ssl]
server = 104-version-negotiation-server
client = 104-version-negotiation-client

[104-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[104-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-104]
ExpectedResult = ServerFail


# ===========================================================

[105-version-negotiation]
ssl_conf = 105-version-negotiation-ssl

[105-version-negotiation-ssl]
server = 105-version-negotiation-server
client = 105-version-negotiation-client

[105-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[105-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-105]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[106-version-negotiation]
ssl_conf = 106-version-negotiation-ssl

[106-version-negotiation-ssl]
server = 106-version-negotiation-server
client = 106-version-negotiation-client

[106-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[106-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-106]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[107-version-negotiation]
ssl_conf = 107-version-negotiation-ssl

[107-version-negotiation-ssl]
server = 107-version-negotiation-server
client = 107-version-negotiation-client

[107-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[107-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-107]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[108-version-negotiation]
ssl_conf = 108-version-negotiation-ssl

[108-version-negotiation-ssl]
server = 108-version-negotiation-server
client = 108-version-negotiation-client

[108-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[108-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-108]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[109-version-negotiation]
ssl_conf = 109-version-negotiation-ssl

[109-version-negotiation-ssl]
server = 109-version-negotiation-server
client = 109-version-negotiation-client

[109-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[109-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-109]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[110-version-negotiation]
ssl_conf = 110-version-negotiation-ssl

[110-version-negotiation-ssl]
server = 110-version-negotiation-server
client = 110-version-negotiation-client

[110-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[110-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-110]
ExpectedResult = ServerFail


# ===========================================================

[111-version-negotiation]
ssl_conf = 111-version-negotiation-ssl

[111-version-negotiation-ssl]
server = 111-version-negotiation-server
client = 111-version-negotiation-client

[111-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[111-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-111]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[112-version-negotiation]
ssl_conf = 112-version-negotiation-ssl

[112-version-negotiation-ssl]
server = 112-version-negotiation-server
client = 112-version-negotiation-client

[112-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[112-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-112]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[113-version-negotiation]
ssl_conf = 113-version-negotiation-ssl

[113-version-negotiation-ssl]
server = 113-version-negotiation-server
client = 113-version-negotiation-client

[113-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[113-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-113]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[114-version-negotiation]
ssl_conf = 114-version-negotiation-ssl

[114-version-negotiation-ssl]
server = 114-version-negotiation-server
client = 114-version-negotiation-client

[114-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[114-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-114]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[115-version-negotiation]
ssl_conf = 115-version-negotiation-ssl

[115-version-negotiation-ssl]
server = 115-version-negotiation-server
client = 115-version-negotiation-client

[115-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[115-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-115]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[116-version-negotiation]
ssl_conf = 116-version-negotiation-ssl

[116-version-negotiation-ssl]
server = 116-version-negotiation-server
client = 116-version-negotiation-client

[116-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[116-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-116]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[117-version-negotiation]
ssl_conf = 117-version-negotiation-ssl

[117-version-negotiation-ssl]
server = 117-version-negotiation-server
client = 117-version-negotiation-client

[117-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[117-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-117]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[118-version-negotiation]
ssl_conf = 118-version-negotiation-ssl

[118-version-negotiation-ssl]
server = 118-version-negotiation-server
client = 118-version-negotiation-client

[118-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[118-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-118]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[119-version-negotiation]
ssl_conf = 119-version-negotiation-ssl

[119-version-negotiation-ssl]
server = 119-version-negotiation-server
client = 119-version-negotiation-client

[119-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[119-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-119]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[120-version-negotiation]
ssl_conf = 120-version-negotiation-ssl

[120-version-negotiation-ssl]
server = 120-version-negotiation-server
client = 120-version-negotiation-client

[120-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[120-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-120]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[121-version-negotiation]
ssl_conf = 121-version-negotiation-ssl

[121-version-negotiation-ssl]
server = 121-version-negotiation-server
client = 121-version-negotiation-client

[121-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[121-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-121]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[122-version-negotiation]
ssl_conf = 122-version-negotiation-ssl

[122-version-negotiation-ssl]
server = 122-version-negotiation-server
client = 122-version-negotiation-client

[122-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[122-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-122]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[123-version-negotiation]
ssl_conf = 123-version-negotiation-ssl

[123-version-negotiation-ssl]
server = 123-version-negotiation-server
client = 123-version-negotiation-client

[123-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[123-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-123]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[124-version-negotiation]
ssl_conf = 124-version-negotiation-ssl

[124-version-negotiation-ssl]
server = 124-version-negotiation-server
client = 124-version-negotiation-client

[124-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[124-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-124]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[125-version-negotiation]
ssl_conf = 125-version-negotiation-ssl

[125-version-negotiation-ssl]
server = 125-version-negotiation-server
client = 125-version-negotiation-client

[125-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[125-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-125]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[126-version-negotiation]
ssl_conf = 126-version-negotiation-ssl

[126-version-negotiation-ssl]
server = 126-version-negotiation-server
client = 126-version-negotiation-client

[126-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[126-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-126]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[127-version-negotiation]
ssl_conf = 127-version-negotiation-ssl

[127-version-negotiation-ssl]
server = 127-version-negotiation-server
client = 127-version-negotiation-client

[127-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[127-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-127]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[128-version-negotiation]
ssl_conf = 128-version-negotiation-ssl

[128-version-negotiation-ssl]
server = 128-version-negotiation-server
client = 128-version-negotiation-client

[128-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[128-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-128]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[129-version-negotiation]
ssl_conf = 129-version-negotiation-ssl

[129-version-negotiation-ssl]
server = 129-version-negotiation-server
client = 129-version-negotiation-client

[129-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[129-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-129]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[130-version-negotiation]
ssl_conf = 130-version-negotiation-ssl

[130-version-negotiation-ssl]
server = 130-version-negotiation-server
client = 130-version-negotiation-client

[130-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[130-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-130]
ExpectedResult = ServerFail


# ===========================================================

[131-version-negotiation]
ssl_conf = 131-version-negotiation-ssl

[131-version-negotiation-ssl]
server = 131-version-negotiation-server
client = 131-version-negotiation-client

[131-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[131-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-131]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[132-version-negotiation]
ssl_conf = 132-version-negotiation-ssl

[132-version-negotiation-ssl]
server = 132-version-negotiation-server
client = 132-version-negotiation-client

[132-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[132-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-132]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[133-version-negotiation]
ssl_conf = 133-version-negotiation-ssl

[133-version-negotiation-ssl]
server = 133-version-negotiation-server
client = 133-version-negotiation-client

[133-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[133-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-133]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[134-version-negotiation]
ssl_conf = 134-version-negotiation-ssl

[134-version-negotiation-ssl]
server = 134-version-negotiation-server
client = 134-version-negotiation-client

[134-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[134-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-134]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[135-version-negotiation]
ssl_conf = 135-version-negotiation-ssl

[135-version-negotiation-ssl]
server = 135-version-negotiation-server
client = 135-version-negotiation-client

[135-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[135-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-135]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[136-version-negotiation]
ssl_conf = 136-version-negotiation-ssl

[136-version-negotiation-ssl]
server = 136-version-negotiation-server
client = 136-version-negotiation-client

[136-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[136-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-136]
ExpectedResult = ServerFail


# ===========================================================

[137-version-negotiation]
ssl_conf = 137-version-negotiation-ssl

[137-version-negotiation-ssl]
server = 137-version-negotiation-server
client = 137-version-negotiation-client

[137-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[137-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-137]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[138-version-negotiation]
ssl_conf = 138-version-negotiation-ssl

[138-version-negotiation-ssl]
server = 138-version-negotiation-server
client = 138-version-negotiation-client

[138-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[138-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-138]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[139-version-negotiation]
ssl_conf = 139-version-negotiation-ssl

[139-version-negotiation-ssl]
server = 139-version-negotiation-server
client = 139-version-negotiation-client

[139-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[139-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-139]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[140-version-negotiation]
ssl_conf = 140-version-negotiation-ssl

[140-version-negotiation-ssl]
server = 140-version-negotiation-server
client = 140-version-negotiation-client

[140-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[140-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-140]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[141-version-negotiation]
ssl_conf = 141-version-negotiation-ssl

[141-version-negotiation-ssl]
server = 141-version-negotiation-server
client = 141-version-negotiation-client

[141-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[141-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-141]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[142-version-negotiation]
ssl_conf = 142-version-negotiation-ssl

[142-version-negotiation-ssl]
server = 142-version-negotiation-server
client = 142-version-negotiation-client

[142-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[142-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-142]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[143-version-negotiation]
ssl_conf = 143-version-negotiation-ssl

[143-version-negotiation-ssl]
server = 143-version-negotiation-server
client = 143-version-negotiation-client

[143-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[143-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-143]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[144-version-negotiation]
ssl_conf = 144-version-negotiation-ssl

[144-version-negotiation-ssl]
server = 144-version-negotiation-server
client = 144-version-negotiation-client

[144-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[144-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-144]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[145-version-negotiation]
ssl_conf = 145-version-negotiation-ssl

[145-version-negotiation-ssl]
server = 145-version-negotiation-server
client = 145-version-negotiation-client

[145-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[145-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-145]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[146-version-negotiation]
ssl_conf = 146-version-negotiation-ssl

[146-version-negotiation-ssl]
server = 146-version-negotiation-server
client = 146-version-negotiation-client

[146-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[146-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-146]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[147-version-negotiation]
ssl_conf = 147-version-negotiation-ssl

[147-version-negotiation-ssl]
server = 147-version-negotiation-server
client = 147-version-negotiation-client

[147-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[147-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-147]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[148-version-negotiation]
ssl_conf = 148-version-negotiation-ssl

[148-version-negotiation-ssl]
server = 148-version-negotiation-server
client = 148-version-negotiation-client

[148-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[148-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-148]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[149-version-negotiation]
ssl_conf = 149-version-negotiation-ssl

[149-version-negotiation-ssl]
server = 149-version-negotiation-server
client = 149-version-negotiation-client

[149-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[149-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-149]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[150-version-negotiation]
ssl_conf = 150-version-negotiation-ssl

[150-version-negotiation-ssl]
server = 150-version-negotiation-server
client = 150-version-negotiation-client

[150-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[150-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-150]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[151-version-negotiation]
ssl_conf = 151-version-negotiation-ssl

[151-version-negotiation-ssl]
server = 151-version-negotiation-server
client = 151-version-negotiation-client

[151-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[151-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-151]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[152-version-negotiation]
ssl_conf = 152-version-negotiation-ssl

[152-version-negotiation-ssl]
server = 152-version-negotiation-server
client = 152-version-negotiation-client

[152-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[152-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-152]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[153-version-negotiation]
ssl_conf = 153-version-negotiation-ssl

[153-version-negotiation-ssl]
server = 153-version-negotiation-server
client = 153-version-negotiation-client

[153-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[153-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-153]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[154-version-negotiation]
ssl_conf = 154-version-negotiation-ssl

[154-version-negotiation-ssl]
server = 154-version-negotiation-server
client = 154-version-negotiation-client

[154-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[154-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-154]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[155-version-negotiation]
ssl_conf = 155-version-negotiation-ssl

[155-version-negotiation-ssl]
server = 155-version-negotiation-server
client = 155-version-negotiation-client

[155-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[155-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-155]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[156-version-negotiation]
ssl_conf = 156-version-negotiation-ssl

[156-version-negotiation-ssl]
server = 156-version-negotiation-server
client = 156-version-negotiation-client

[156-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[156-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-156]
ExpectedResult = ClientFail


# ===========================================================

[157-version-negotiation]
ssl_conf = 157-version-negotiation-ssl

[157-version-negotiation-ssl]
server = 157-version-negotiation-server
client = 157-version-negotiation-client

[157-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[157-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-157]
ExpectedResult = ClientFail


# ===========================================================

[158-version-negotiation]
ssl_conf = 158-version-negotiation-ssl

[158-version-negotiation-ssl]
server = 158-version-negotiation-server
client = 158-version-negotiation-client

[158-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[158-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-158]
ExpectedResult = ClientFail


# ===========================================================

[159-version-negotiation]
ssl_conf = 159-version-negotiation-ssl

[159-version-negotiation-ssl]
server = 159-version-negotiation-server
client = 159-version-negotiation-client

[159-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[159-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-159]
ExpectedResult = ClientFail


# ===========================================================

[160-version-negotiation]
ssl_conf = 160-version-negotiation-ssl

[160-version-negotiation-ssl]
server = 160-version-negotiation-server
client = 160-version-negotiation-client

[160-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[160-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-160]
ExpectedResult = ClientFail


# ===========================================================

[161-version-negotiation]
ssl_conf = 161-version-negotiation-ssl

[161-version-negotiation-ssl]
server = 161-version-negotiation-server
client = 161-version-negotiation-client

[161-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[161-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-161]
ExpectedResult = ClientFail


# ===========================================================

[162-version-negotiation]
ssl_conf = 162-version-negotiation-ssl

[162-version-negotiation-ssl]
server = 162-version-negotiation-server
client = 162-version-negotiation-client

[162-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[162-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-162]
ExpectedResult = ClientFail


# ===========================================================

[163-version-negotiation]
ssl_conf = 163-version-negotiation-ssl

[163-version-negotiation-ssl]
server = 163-version-negotiation-server
client = 163-version-negotiation-client

[163-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[163-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-163]
ExpectedResult = ClientFail


# ===========================================================

[164-version-negotiation]
ssl_conf = 164-version-negotiation-ssl

[164-version-negotiation-ssl]
server = 164-version-negotiation-server
client = 164-version-negotiation-client

[164-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[164-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-164]
ExpectedResult = ClientFail


# ===========================================================

[165-version-negotiation]
ssl_conf = 165-version-negotiation-ssl

[165-version-negotiation-ssl]
server = 165-version-negotiation-server
client = 165-version-negotiation-client

[165-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[165-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-165]
ExpectedResult = ClientFail


# ===========================================================

[166-version-negotiation]
ssl_conf = 166-version-negotiation-ssl

[166-version-negotiation-ssl]
server = 166-version-negotiation-server
client = 166-version-negotiation-client

[166-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[166-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-166]
ExpectedResult = ClientFail


# ===========================================================

[167-version-negotiation]
ssl_conf = 167-version-negotiation-ssl

[167-version-negotiation-ssl]
server = 167-version-negotiation-server
client = 167-version-negotiation-client

[167-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[167-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-167]
ExpectedResult = ClientFail


# ===========================================================

[168-version-negotiation]
ssl_conf = 168-version-negotiation-ssl

[168-version-negotiation-ssl]
server = 168-version-negotiation-server
client = 168-version-negotiation-client

[168-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[168-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-168]
ExpectedResult = ClientFail


# ===========================================================

[169-version-negotiation]
ssl_conf = 169-version-negotiation-ssl

[169-version-negotiation-ssl]
server = 169-version-negotiation-server
client = 169-version-negotiation-client

[169-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[169-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-169]
ExpectedResult = ClientFail


# ===========================================================

[170-version-negotiation]
ssl_conf = 170-version-negotiation-ssl

[170-version-negotiation-ssl]
server = 170-version-negotiation-server
client = 170-version-negotiation-client

[170-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[170-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-170]
ExpectedResult = ClientFail


# ===========================================================

[171-version-negotiation]
ssl_conf = 171-version-negotiation-ssl

[171-version-negotiation-ssl]
server = 171-version-negotiation-server
client = 171-version-negotiation-client

[171-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[171-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-171]
ExpectedResult = ClientFail


# ===========================================================

[172-version-negotiation]
ssl_conf = 172-version-negotiation-ssl

[172-version-negotiation-ssl]
server = 172-version-negotiation-server
client = 172-version-negotiation-client

[172-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[172-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-172]
ExpectedResult = ClientFail


# ===========================================================

[173-version-negotiation]
ssl_conf = 173-version-negotiation-ssl

[173-version-negotiation-ssl]
server = 173-version-negotiation-server
client = 173-version-negotiation-client

[173-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[173-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-173]
ExpectedResult = ClientFail


# ===========================================================

[174-version-negotiation]
ssl_conf = 174-version-negotiation-ssl

[174-version-negotiation-ssl]
server = 174-version-negotiation-server
client = 174-version-negotiation-client

[174-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[174-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-174]
ExpectedResult = ClientFail


# ===========================================================

[175-version-negotiation]
ssl_conf = 175-version-negotiation-ssl

[175-version-negotiation-ssl]
server = 175-version-negotiation-server
client = 175-version-negotiation-client

[175-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[175-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-175]
ExpectedResult = ClientFail


# ===========================================================

[176-version-negotiation]
ssl_conf = 176-version-negotiation-ssl

[176-version-negotiation-ssl]
server = 176-version-negotiation-server
client = 176-version-negotiation-client

[176-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[176-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-176]
ExpectedResult = ClientFail


# ===========================================================

[177-version-negotiation]
ssl_conf = 177-version-negotiation-ssl

[177-version-negotiation-ssl]
server = 177-version-negotiation-server
client = 177-version-negotiation-client

[177-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[177-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-177]
ExpectedResult = ClientFail


# ===========================================================

[178-version-negotiation]
ssl_conf = 178-version-negotiation-ssl

[178-version-negotiation-ssl]
server = 178-version-negotiation-server
client = 178-version-negotiation-client

[178-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[178-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-178]
ExpectedResult = ClientFail


# ===========================================================

[179-version-negotiation]
ssl_conf = 179-version-negotiation-ssl

[179-version-negotiation-ssl]
server = 179-version-negotiation-server
client = 179-version-negotiation-client

[179-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[179-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-179]
ExpectedResult = ClientFail


# ===========================================================

[180-version-negotiation]
ssl_conf = 180-version-negotiation-ssl

[180-version-negotiation-ssl]
server = 180-version-negotiation-server
client = 180-version-negotiation-client

[180-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[180-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-180]
ExpectedResult = ClientFail


# ===========================================================

[181-version-negotiation]
ssl_conf = 181-version-negotiation-ssl

[181-version-negotiation-ssl]
server = 181-version-negotiation-server
client = 181-version-negotiation-client

[181-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[181-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-181]
ExpectedResult = ClientFail


# ===========================================================

[182-version-negotiation]
ssl_conf = 182-version-negotiation-ssl

[182-version-negotiation-ssl]
server = 182-version-negotiation-server
client = 182-version-negotiation-client

[182-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[182-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-182]
ExpectedResult = ServerFail


# ===========================================================

[183-version-negotiation]
ssl_conf = 183-version-negotiation-ssl

[183-version-negotiation-ssl]
server = 183-version-negotiation-server
client = 183-version-negotiation-client

[183-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[183-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-183]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[184-version-negotiation]
ssl_conf = 184-version-negotiation-ssl

[184-version-negotiation-ssl]
server = 184-version-negotiation-server
client = 184-version-negotiation-client

[184-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[184-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-184]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[185-version-negotiation]
ssl_conf = 185-version-negotiation-ssl

[185-version-negotiation-ssl]
server = 185-version-negotiation-server
client = 185-version-negotiation-client

[185-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[185-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-185]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[186-version-negotiation]
ssl_conf = 186-version-negotiation-ssl

[186-version-negotiation-ssl]
server = 186-version-negotiation-server
client = 186-version-negotiation-client

[186-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[186-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-186]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[187-version-negotiation]
ssl_conf = 187-version-negotiation-ssl

[187-version-negotiation-ssl]
server = 187-version-negotiation-server
client = 187-version-negotiation-client

[187-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[187-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-187]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[188-version-negotiation]
ssl_conf = 188-version-negotiation-ssl

[188-version-negotiation-ssl]
server = 188-version-negotiation-server
client = 188-version-negotiation-client

[188-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[188-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-188]
ExpectedResult = ServerFail


# ===========================================================

[189-version-negotiation]
ssl_conf = 189-version-negotiation-ssl

[189-version-negotiation-ssl]
server = 189-version-negotiation-server
client = 189-version-negotiation-client

[189-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[189-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-189]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[190-version-negotiation]
ssl_conf = 190-version-negotiation-ssl

[190-version-negotiation-ssl]
server = 190-version-negotiation-server
client = 190-version-negotiation-client

[190-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[190-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-190]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[191-version-negotiation]
ssl_conf = 191-version-negotiation-ssl

[191-version-negotiation-ssl]
server = 191-version-negotiation-server
client = 191-version-negotiation-client

[191-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[191-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-191]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[192-version-negotiation]
ssl_conf = 192-version-negotiation-ssl

[192-version-negotiation-ssl]
server = 192-version-negotiation-server
client = 192-version-negotiation-client

[192-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[192-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-192]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[193-version-negotiation]
ssl_conf = 193-version-negotiation-ssl

[193-version-negotiation-ssl]
server = 193-version-negotiation-server
client = 193-version-negotiation-client

[193-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[193-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-193]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[194-version-negotiation]
ssl_conf = 194-version-negotiation-ssl

[194-version-negotiation-ssl]
server = 194-version-negotiation-server
client = 194-version-negotiation-client

[194-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[194-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-194]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[195-version-negotiation]
ssl_conf = 195-version-negotiation-ssl

[195-version-negotiation-ssl]
server = 195-version-negotiation-server
client = 195-version-negotiation-client

[195-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[195-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-195]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[196-version-negotiation]
ssl_conf = 196-version-negotiation-ssl

[196-version-negotiation-ssl]
server = 196-version-negotiation-server
client = 196-version-negotiation-client

[196-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[196-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-196]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[197-version-negotiation]
ssl_conf = 197-version-negotiation-ssl

[197-version-negotiation-ssl]
server = 197-version-negotiation-server
client = 197-version-negotiation-client

[197-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[197-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-197]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[198-version-negotiation]
ssl_conf = 198-version-negotiation-ssl

[198-version-negotiation-ssl]
server = 198-version-negotiation-server
client = 198-version-negotiation-client

[198-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[198-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-198]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[199-version-negotiation]
ssl_conf = 199-version-negotiation-ssl

[199-version-negotiation-ssl]
server = 199-version-negotiation-server
client = 199-version-negotiation-client

[199-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[199-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-199]
ExpectedResult = ServerFail


# ===========================================================

[200-version-negotiation]
ssl_conf = 200-version-negotiation-ssl

[200-version-negotiation-ssl]
server = 200-version-negotiation-server
client = 200-version-negotiation-client

[200-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[200-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-200]
ExpectedResult = ServerFail


# ===========================================================

[201-version-negotiation]
ssl_conf = 201-version-negotiation-ssl

[201-version-negotiation-ssl]
server = 201-version-negotiation-server
client = 201-version-negotiation-client

[201-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[201-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-201]
ExpectedResult = ServerFail


# ===========================================================

[202-version-negotiation]
ssl_conf = 202-version-negotiation-ssl

[202-version-negotiation-ssl]
server = 202-version-negotiation-server
client = 202-version-negotiation-client

[202-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[202-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-202]
ExpectedResult = ServerFail


# ===========================================================

[203-version-negotiation]
ssl_conf = 203-version-negotiation-ssl

[203-version-negotiation-ssl]
server = 203-version-negotiation-server
client = 203-version-negotiation-client

[203-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[203-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-203]
ExpectedResult = ServerFail


# ===========================================================

[204-version-negotiation]
ssl_conf = 204-version-negotiation-ssl

[204-version-negotiation-ssl]
server = 204-version-negotiation-server
client = 204-version-negotiation-client

[204-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[204-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-204]
ExpectedResult = ServerFail


# ===========================================================

[205-version-negotiation]
ssl_conf = 205-version-negotiation-ssl

[205-version-negotiation-ssl]
server = 205-version-negotiation-server
client = 205-version-negotiation-client

[205-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[205-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-205]
ExpectedResult = ServerFail


# ===========================================================

[206-version-negotiation]
ssl_conf = 206-version-negotiation-ssl

[206-version-negotiation-ssl]
server = 206-version-negotiation-server
client = 206-version-negotiation-client

[206-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[206-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-206]
ExpectedResult = ServerFail


# ===========================================================

[207-version-negotiation]
ssl_conf = 207-version-negotiation-ssl

[207-version-negotiation-ssl]
server = 207-version-negotiation-server
client = 207-version-negotiation-client

[207-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[207-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-207]
ExpectedResult = ServerFail


# ===========================================================

[208-version-negotiation]
ssl_conf = 208-version-negotiation-ssl

[208-version-negotiation-ssl]
server = 208-version-negotiation-server
client = 208-version-negotiation-client

[208-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[208-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-208]
ExpectedResult = ServerFail


# ===========================================================

[209-version-negotiation]
ssl_conf = 209-version-negotiation-ssl

[209-version-negotiation-ssl]
server = 209-version-negotiation-server
client = 209-version-negotiation-client

[209-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[209-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-209]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[210-version-negotiation]
ssl_conf = 210-version-negotiation-ssl

[210-version-negotiation-ssl]
server = 210-version-negotiation-server
client = 210-version-negotiation-client

[210-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[210-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-210]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[211-version-negotiation]
ssl_conf = 211-version-negotiation-ssl

[211-version-negotiation-ssl]
server = 211-version-negotiation-server
client = 211-version-negotiation-client

[211-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[211-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-211]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[212-version-negotiation]
ssl_conf = 212-version-negotiation-ssl

[212-version-negotiation-ssl]
server = 212-version-negotiation-server
client = 212-version-negotiation-client

[212-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[212-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-212]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[213-version-negotiation]
ssl_conf = 213-version-negotiation-ssl

[213-version-negotiation-ssl]
server = 213-version-negotiation-server
client = 213-version-negotiation-client

[213-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[213-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-213]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[214-version-negotiation]
ssl_conf = 214-version-negotiation-ssl

[214-version-negotiation-ssl]
server = 214-version-negotiation-server
client = 214-version-negotiation-client

[214-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[214-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-214]
ExpectedResult = ServerFail


# ===========================================================

[215-version-negotiation]
ssl_conf = 215-version-negotiation-ssl

[215-version-negotiation-ssl]
server = 215-version-negotiation-server
client = 215-version-negotiation-client

[215-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[215-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-215]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[216-version-negotiation]
ssl_conf = 216-version-negotiation-ssl

[216-version-negotiation-ssl]
server = 216-version-negotiation-server
client = 216-version-negotiation-client

[216-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[216-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-216]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[217-version-negotiation]
ssl_conf = 217-version-negotiation-ssl

[217-version-negotiation-ssl]
server = 217-version-negotiation-server
client = 217-version-negotiation-client

[217-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[217-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-217]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[218-version-negotiation]
ssl_conf = 218-version-negotiation-ssl

[218-version-negotiation-ssl]
server = 218-version-negotiation-server
client = 218-version-negotiation-client

[218-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[218-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-218]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[219-version-negotiation]
ssl_conf = 219-version-negotiation-ssl

[219-version-negotiation-ssl]
server = 219-version-negotiation-server
client = 219-version-negotiation-client

[219-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[219-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-219]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[220-version-negotiation]
ssl_conf = 220-version-negotiation-ssl

[220-version-negotiation-ssl]
server = 220-version-negotiation-server
client = 220-version-negotiation-client

[220-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[220-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-220]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[221-version-negotiation]
ssl_conf = 221-version-negotiation-ssl

[221-version-negotiation-ssl]
server = 221-version-negotiation-server
client = 221-version-negotiation-client

[221-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[221-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-221]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[222-version-negotiation]
ssl_conf = 222-version-negotiation-ssl

[222-version-negotiation-ssl]
server = 222-version-negotiation-server
client = 222-version-negotiation-client

[222-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[222-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-222]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[223-version-negotiation]
ssl_conf = 223-version-negotiation-ssl

[223-version-negotiation-ssl]
server = 223-version-negotiation-server
client = 223-version-negotiation-client

[223-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[223-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-223]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[224-version-negotiation]
ssl_conf = 224-version-negotiation-ssl

[224-version-negotiation-ssl]
server = 224-version-negotiation-server
client = 224-version-negotiation-client

[224-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[224-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-224]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[225-version-negotiation]
ssl_conf = 225-version-negotiation-ssl

[225-version-negotiation-ssl]
server = 225-version-negotiation-server
client = 225-version-negotiation-client

[225-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[225-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-225]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[226-version-negotiation]
ssl_conf = 226-version-negotiation-ssl

[226-version-negotiation-ssl]
server = 226-version-negotiation-server
client = 226-version-negotiation-client

[226-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[226-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-226]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[227-version-negotiation]
ssl_conf = 227-version-negotiation-ssl

[227-version-negotiation-ssl]
server = 227-version-negotiation-server
client = 227-version-negotiation-client

[227-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[227-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-227]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[228-version-negotiation]
ssl_conf = 228-version-negotiation-ssl

[228-version-negotiation-ssl]
server = 228-version-negotiation-server
client = 228-version-negotiation-client

[228-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[228-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-228]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[229-version-negotiation]
ssl_conf = 229-version-negotiation-ssl

[229-version-negotiation-ssl]
server = 229-version-negotiation-server
client = 229-version-negotiation-client

[229-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[229-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-229]
ExpectedResult = ServerFail


# ===========================================================

[230-version-negotiation]
ssl_conf = 230-version-negotiation-ssl

[230-version-negotiation-ssl]
server = 230-version-negotiation-server
client = 230-version-negotiation-client

[230-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[230-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-230]
ExpectedResult = ServerFail


# ===========================================================

[231-version-negotiation]
ssl_conf = 231-version-negotiation-ssl

[231-version-negotiation-ssl]
server = 231-version-negotiation-server
client = 231-version-negotiation-client

[231-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[231-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-231]
ExpectedResult = ServerFail


# ===========================================================

[232-version-negotiation]
ssl_conf = 232-version-negotiation-ssl

[232-version-negotiation-ssl]
server = 232-version-negotiation-server
client = 232-version-negotiation-client

[232-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[232-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-232]
ExpectedResult = ServerFail


# ===========================================================

[233-version-negotiation]
ssl_conf = 233-version-negotiation-ssl

[233-version-negotiation-ssl]
server = 233-version-negotiation-server
client = 233-version-negotiation-client

[233-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[233-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-233]
ExpectedResult = ServerFail


# ===========================================================

[234-version-negotiation]
ssl_conf = 234-version-negotiation-ssl

[234-version-negotiation-ssl]
server = 234-version-negotiation-server
client = 234-version-negotiation-client

[234-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[234-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-234]
ExpectedResult = ServerFail


# ===========================================================

[235-version-negotiation]
ssl_conf = 235-version-negotiation-ssl

[235-version-negotiation-ssl]
server = 235-version-negotiation-server
client = 235-version-negotiation-client

[235-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[235-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-235]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[236-version-negotiation]
ssl_conf = 236-version-negotiation-ssl

[236-version-negotiation-ssl]
server = 236-version-negotiation-server
client = 236-version-negotiation-client

[236-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[236-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-236]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[237-version-negotiation]
ssl_conf = 237-version-negotiation-ssl

[237-version-negotiation-ssl]
server = 237-version-negotiation-server
client = 237-version-negotiation-client

[237-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[237-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-237]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[238-version-negotiation]
ssl_conf = 238-version-negotiation-ssl

[238-version-negotiation-ssl]
server = 238-version-negotiation-server
client = 238-version-negotiation-client

[238-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[238-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-238]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[239-version-negotiation]
ssl_conf = 239-version-negotiation-ssl

[239-version-negotiation-ssl]
server = 239-version-negotiation-server
client = 239-version-negotiation-client

[239-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[239-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-239]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[240-version-negotiation]
ssl_conf = 240-version-negotiation-ssl

[240-version-negotiation-ssl]
server = 240-version-negotiation-server
client = 240-version-negotiation-client

[240-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[240-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-240]
ExpectedResult = ServerFail


# ===========================================================

[241-version-negotiation]
ssl_conf = 241-version-negotiation-ssl

[241-version-negotiation-ssl]
server = 241-version-negotiation-server
client = 241-version-negotiation-client

[241-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[241-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-241]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[242-version-negotiation]
ssl_conf = 242-version-negotiation-ssl

[242-version-negotiation-ssl]
server = 242-version-negotiation-server
client = 242-version-negotiation-client

[242-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[242-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-242]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[243-version-negotiation]
ssl_conf = 243-version-negotiation-ssl

[243-version-negotiation-ssl]
server = 243-version-negotiation-server
client = 243-version-negotiation-client

[243-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[243-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-243]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[244-version-negotiation]
ssl_conf = 244-version-negotiation-ssl

[244-version-negotiation-ssl]
server = 244-version-negotiation-server
client = 244-version-negotiation-client

[244-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[244-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-244]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[245-version-negotiation]
ssl_conf = 245-version-negotiation-ssl

[245-version-negotiation-ssl]
server = 245-version-negotiation-server
client = 245-version-negotiation-client

[245-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[245-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-245]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[246-version-negotiation]
ssl_conf = 246-version-negotiation-ssl

[246-version-negotiation-ssl]
server = 246-version-negotiation-server
client = 246-version-negotiation-client

[246-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[246-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-246]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[247-version-negotiation]
ssl_conf = 247-version-negotiation-ssl

[247-version-negotiation-ssl]
server = 247-version-negotiation-server
client = 247-version-negotiation-client

[247-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[247-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-247]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[248-version-negotiation]
ssl_conf = 248-version-negotiation-ssl

[248-version-negotiation-ssl]
server = 248-version-negotiation-server
client = 248-version-negotiation-client

[248-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[248-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-248]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[249-version-negotiation]
ssl_conf = 249-version-negotiation-ssl

[249-version-negotiation-ssl]
server = 249-version-negotiation-server
client = 249-version-negotiation-client

[249-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[249-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-249]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[250-version-negotiation]
ssl_conf = 250-version-negotiation-ssl

[250-version-negotiation-ssl]
server = 250-version-negotiation-server
client = 250-version-negotiation-client

[250-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[250-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-250]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[251-version-negotiation]
ssl_conf = 251-version-negotiation-ssl

[251-version-negotiation-ssl]
server = 251-version-negotiation-server
client = 251-version-negotiation-client

[251-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[251-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-251]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[252-version-negotiation]
ssl_conf = 252-version-negotiation-ssl

[252-version-negotiation-ssl]
server = 252-version-negotiation-server
client = 252-version-negotiation-client

[252-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[252-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-252]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[253-version-negotiation]
ssl_conf = 253-version-negotiation-ssl

[253-version-negotiation-ssl]
server = 253-version-negotiation-server
client = 253-version-negotiation-client

[253-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[253-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-253]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[254-version-negotiation]
ssl_conf = 254-version-negotiation-ssl

[254-version-negotiation-ssl]
server = 254-version-negotiation-server
client = 254-version-negotiation-client

[254-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[254-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-254]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[255-version-negotiation]
ssl_conf = 255-version-negotiation-ssl

[255-version-negotiation-ssl]
server = 255-version-negotiation-server
client = 255-version-negotiation-client

[255-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[255-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-255]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[256-version-negotiation]
ssl_conf = 256-version-negotiation-ssl

[256-version-negotiation-ssl]
server = 256-version-negotiation-server
client = 256-version-negotiation-client

[256-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[256-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-256]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[257-version-negotiation]
ssl_conf = 257-version-negotiation-ssl

[257-version-negotiation-ssl]
server = 257-version-negotiation-server
client = 257-version-negotiation-client

[257-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[257-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-257]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[258-version-negotiation]
ssl_conf = 258-version-negotiation-ssl

[258-version-negotiation-ssl]
server = 258-version-negotiation-server
client = 258-version-negotiation-client

[258-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[258-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-258]
ExpectedResult = ServerFail


# ===========================================================

[259-version-negotiation]
ssl_conf = 259-version-negotiation-ssl

[259-version-negotiation-ssl]
server = 259-version-negotiation-server
client = 259-version-negotiation-client

[259-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[259-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-259]
ExpectedResult = ServerFail


# ===========================================================

[260-version-negotiation]
ssl_conf = 260-version-negotiation-ssl

[260-version-negotiation-ssl]
server = 260-version-negotiation-server
client = 260-version-negotiation-client

[260-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[260-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-260]
ExpectedResult = ServerFail


# ===========================================================

[261-version-negotiation]
ssl_conf = 261-version-negotiation-ssl

[261-version-negotiation-ssl]
server = 261-version-negotiation-server
client = 261-version-negotiation-client

[261-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[261-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-261]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[262-version-negotiation]
ssl_conf = 262-version-negotiation-ssl

[262-version-negotiation-ssl]
server = 262-version-negotiation-server
client = 262-version-negotiation-client

[262-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[262-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-262]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[263-version-negotiation]
ssl_conf = 263-version-negotiation-ssl

[263-version-negotiation-ssl]
server = 263-version-negotiation-server
client = 263-version-negotiation-client

[263-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[263-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-263]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[264-version-negotiation]
ssl_conf = 264-version-negotiation-ssl

[264-version-negotiation-ssl]
server = 264-version-negotiation-server
client = 264-version-negotiation-client

[264-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[264-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-264]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[265-version-negotiation]
ssl_conf = 265-version-negotiation-ssl

[265-version-negotiation-ssl]
server = 265-version-negotiation-server
client = 265-version-negotiation-client

[265-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[265-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-265]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[266-version-negotiation]
ssl_conf = 266-version-negotiation-ssl

[266-version-negotiation-ssl]
server = 266-version-negotiation-server
client = 266-version-negotiation-client

[266-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[266-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-266]
ExpectedResult = ServerFail


# ===========================================================

[267-version-negotiation]
ssl_conf = 267-version-negotiation-ssl

[267-version-negotiation-ssl]
server = 267-version-negotiation-server
client = 267-version-negotiation-client

[267-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[267-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-267]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[268-version-negotiation]
ssl_conf = 268-version-negotiation-ssl

[268-version-negotiation-ssl]
server = 268-version-negotiation-server
client = 268-version-negotiation-client

[268-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[268-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-268]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[269-version-negotiation]
ssl_conf = 269-version-negotiation-ssl

[269-version-negotiation-ssl]
server = 269-version-negotiation-server
client = 269-version-negotiation-client

[269-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[269-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-269]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[270-version-negotiation]
ssl_conf = 270-version-negotiation-ssl

[270-version-negotiation-ssl]
server = 270-version-negotiation-server
client = 270-version-negotiation-client

[270-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[270-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-270]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[271-version-negotiation]
ssl_conf = 271-version-negotiation-ssl

[271-version-negotiation-ssl]
server = 271-version-negotiation-server
client = 271-version-negotiation-client

[271-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[271-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-271]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[272-version-negotiation]
ssl_conf = 272-version-negotiation-ssl

[272-version-negotiation-ssl]
server = 272-version-negotiation-server
client = 272-version-negotiation-client

[272-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[272-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-272]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[273-version-negotiation]
ssl_conf = 273-version-negotiation-ssl

[273-version-negotiation-ssl]
server = 273-version-negotiation-server
client = 273-version-negotiation-client

[273-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[273-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-273]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[274-version-negotiation]
ssl_conf = 274-version-negotiation-ssl

[274-version-negotiation-ssl]
server = 274-version-negotiation-server
client = 274-version-negotiation-client

[274-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[274-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-274]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[275-version-negotiation]
ssl_conf = 275-version-negotiation-ssl

[275-version-negotiation-ssl]
server = 275-version-negotiation-server
client = 275-version-negotiation-client

[275-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[275-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-275]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[276-version-negotiation]
ssl_conf = 276-version-negotiation-ssl

[276-version-negotiation-ssl]
server = 276-version-negotiation-server
client = 276-version-negotiation-client

[276-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[276-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-276]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[277-version-negotiation]
ssl_conf = 277-version-negotiation-ssl

[277-version-negotiation-ssl]
server = 277-version-negotiation-server
client = 277-version-negotiation-client

[277-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[277-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-277]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[278-version-negotiation]
ssl_conf = 278-version-negotiation-ssl

[278-version-negotiation-ssl]
server = 278-version-negotiation-server
client = 278-version-negotiation-client

[278-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[278-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-278]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[279-version-negotiation]
ssl_conf = 279-version-negotiation-ssl

[279-version-negotiation-ssl]
server = 279-version-negotiation-server
client = 279-version-negotiation-client

[279-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[279-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-279]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[280-version-negotiation]
ssl_conf = 280-version-negotiation-ssl

[280-version-negotiation-ssl]
server = 280-version-negotiation-server
client = 280-version-negotiation-client

[280-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[280-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-280]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[281-version-negotiation]
ssl_conf = 281-version-negotiation-ssl

[281-version-negotiation-ssl]
server = 281-version-negotiation-server
client = 281-version-negotiation-client

[281-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[281-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-281]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[282-version-negotiation]
ssl_conf = 282-version-negotiation-ssl

[282-version-negotiation-ssl]
server = 282-version-negotiation-server
client = 282-version-negotiation-client

[282-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[282-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-282]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[283-version-negotiation]
ssl_conf = 283-version-negotiation-ssl

[283-version-negotiation-ssl]
server = 283-version-negotiation-server
client = 283-version-negotiation-client

[283-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[283-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-283]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[284-version-negotiation]
ssl_conf = 284-version-negotiation-ssl

[284-version-negotiation-ssl]
server = 284-version-negotiation-server
client = 284-version-negotiation-client

[284-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[284-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-284]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[285-version-negotiation]
ssl_conf = 285-version-negotiation-ssl

[285-version-negotiation-ssl]
server = 285-version-negotiation-server
client = 285-version-negotiation-client

[285-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[285-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-285]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[286-version-negotiation]
ssl_conf = 286-version-negotiation-ssl

[286-version-negotiation-ssl]
server = 286-version-negotiation-server
client = 286-version-negotiation-client

[286-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[286-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-286]
ExpectedResult = ServerFail


# ===========================================================

[287-version-negotiation]
ssl_conf = 287-version-negotiation-ssl

[287-version-negotiation-ssl]
server = 287-version-negotiation-server
client = 287-version-negotiation-client

[287-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[287-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-287]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[288-version-negotiation]
ssl_conf = 288-version-negotiation-ssl

[288-version-negotiation-ssl]
server = 288-version-negotiation-server
client = 288-version-negotiation-client

[288-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[288-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-288]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[289-version-negotiation]
ssl_conf = 289-version-negotiation-ssl

[289-version-negotiation-ssl]
server = 289-version-negotiation-server
client = 289-version-negotiation-client

[289-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[289-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-289]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[290-version-negotiation]
ssl_conf = 290-version-negotiation-ssl

[290-version-negotiation-ssl]
server = 290-version-negotiation-server
client = 290-version-negotiation-client

[290-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[290-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-290]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[291-version-negotiation]
ssl_conf = 291-version-negotiation-ssl

[291-version-negotiation-ssl]
server = 291-version-negotiation-server
client = 291-version-negotiation-client

[291-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[291-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-291]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[292-version-negotiation]
ssl_conf = 292-version-negotiation-ssl

[292-version-negotiation-ssl]
server = 292-version-negotiation-server
client = 292-version-negotiation-client

[292-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[292-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-292]
ExpectedResult = ServerFail


# ===========================================================

[293-version-negotiation]
ssl_conf = 293-version-negotiation-ssl

[293-version-negotiation-ssl]
server = 293-version-negotiation-server
client = 293-version-negotiation-client

[293-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[293-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-293]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[294-version-negotiation]
ssl_conf = 294-version-negotiation-ssl

[294-version-negotiation-ssl]
server = 294-version-negotiation-server
client = 294-version-negotiation-client

[294-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[294-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-294]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[295-version-negotiation]
ssl_conf = 295-version-negotiation-ssl

[295-version-negotiation-ssl]
server = 295-version-negotiation-server
client = 295-version-negotiation-client

[295-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[295-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-295]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[296-version-negotiation]
ssl_conf = 296-version-negotiation-ssl

[296-version-negotiation-ssl]
server = 296-version-negotiation-server
client = 296-version-negotiation-client

[296-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[296-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-296]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[297-version-negotiation]
ssl_conf = 297-version-negotiation-ssl

[297-version-negotiation-ssl]
server = 297-version-negotiation-server
client = 297-version-negotiation-client

[297-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[297-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-297]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[298-version-negotiation]
ssl_conf = 298-version-negotiation-ssl

[298-version-negotiation-ssl]
server = 298-version-negotiation-server
client = 298-version-negotiation-client

[298-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[298-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-298]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[299-version-negotiation]
ssl_conf = 299-version-negotiation-ssl

[299-version-negotiation-ssl]
server = 299-version-negotiation-server
client = 299-version-negotiation-client

[299-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[299-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-299]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[300-version-negotiation]
ssl_conf = 300-version-negotiation-ssl

[300-version-negotiation-ssl]
server = 300-version-negotiation-server
client = 300-version-negotiation-client

[300-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[300-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-300]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[301-version-negotiation]
ssl_conf = 301-version-negotiation-ssl

[301-version-negotiation-ssl]
server = 301-version-negotiation-server
client = 301-version-negotiation-client

[301-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[301-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-301]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[302-version-negotiation]
ssl_conf = 302-version-negotiation-ssl

[302-version-negotiation-ssl]
server = 302-version-negotiation-server
client = 302-version-negotiation-client

[302-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[302-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-302]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[303-version-negotiation]
ssl_conf = 303-version-negotiation-ssl

[303-version-negotiation-ssl]
server = 303-version-negotiation-server
client = 303-version-negotiation-client

[303-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[303-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-303]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[304-version-negotiation]
ssl_conf = 304-version-negotiation-ssl

[304-version-negotiation-ssl]
server = 304-version-negotiation-server
client = 304-version-negotiation-client

[304-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[304-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-304]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[305-version-negotiation]
ssl_conf = 305-version-negotiation-ssl

[305-version-negotiation-ssl]
server = 305-version-negotiation-server
client = 305-version-negotiation-client

[305-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[305-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-305]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[306-version-negotiation]
ssl_conf = 306-version-negotiation-ssl

[306-version-negotiation-ssl]
server = 306-version-negotiation-server
client = 306-version-negotiation-client

[306-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[306-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-306]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[307-version-negotiation]
ssl_conf = 307-version-negotiation-ssl

[307-version-negotiation-ssl]
server = 307-version-negotiation-server
client = 307-version-negotiation-client

[307-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[307-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-307]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[308-version-negotiation]
ssl_conf = 308-version-negotiation-ssl

[308-version-negotiation-ssl]
server = 308-version-negotiation-server
client = 308-version-negotiation-client

[308-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[308-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-308]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[309-version-negotiation]
ssl_conf = 309-version-negotiation-ssl

[309-version-negotiation-ssl]
server = 309-version-negotiation-server
client = 309-version-negotiation-client

[309-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[309-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-309]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[310-version-negotiation]
ssl_conf = 310-version-negotiation-ssl

[310-version-negotiation-ssl]
server = 310-version-negotiation-server
client = 310-version-negotiation-client

[310-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[310-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-310]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[311-version-negotiation]
ssl_conf = 311-version-negotiation-ssl

[311-version-negotiation-ssl]
server = 311-version-negotiation-server
client = 311-version-negotiation-client

[311-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[311-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-311]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[312-version-negotiation]
ssl_conf = 312-version-negotiation-ssl

[312-version-negotiation-ssl]
server = 312-version-negotiation-server
client = 312-version-negotiation-client

[312-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[312-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-312]
ExpectedResult = ServerFail


# ===========================================================

[313-version-negotiation]
ssl_conf = 313-version-negotiation-ssl

[313-version-negotiation-ssl]
server = 313-version-negotiation-server
client = 313-version-negotiation-client

[313-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[313-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-313]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[314-version-negotiation]
ssl_conf = 314-version-negotiation-ssl

[314-version-negotiation-ssl]
server = 314-version-negotiation-server
client = 314-version-negotiation-client

[314-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[314-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-314]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[315-version-negotiation]
ssl_conf = 315-version-negotiation-ssl

[315-version-negotiation-ssl]
server = 315-version-negotiation-server
client = 315-version-negotiation-client

[315-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[315-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-315]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[316-version-negotiation]
ssl_conf = 316-version-negotiation-ssl

[316-version-negotiation-ssl]
server = 316-version-negotiation-server
client = 316-version-negotiation-client

[316-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[316-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-316]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[317-version-negotiation]
ssl_conf = 317-version-negotiation-ssl

[317-version-negotiation-ssl]
server = 317-version-negotiation-server
client = 317-version-negotiation-client

[317-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[317-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-317]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[318-version-negotiation]
ssl_conf = 318-version-negotiation-ssl

[318-version-negotiation-ssl]
server = 318-version-negotiation-server
client = 318-version-negotiation-client

[318-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[318-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-318]
ExpectedResult = ServerFail


# ===========================================================

[319-version-negotiation]
ssl_conf = 319-version-negotiation-ssl

[319-version-negotiation-ssl]
server = 319-version-negotiation-server
client = 319-version-negotiation-client

[319-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[319-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-319]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[320-version-negotiation]
ssl_conf = 320-version-negotiation-ssl

[320-version-negotiation-ssl]
server = 320-version-negotiation-server
client = 320-version-negotiation-client

[320-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[320-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-320]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[321-version-negotiation]
ssl_conf = 321-version-negotiation-ssl

[321-version-negotiation-ssl]
server = 321-version-negotiation-server
client = 321-version-negotiation-client

[321-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[321-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-321]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[322-version-negotiation]
ssl_conf = 322-version-negotiation-ssl

[322-version-negotiation-ssl]
server = 322-version-negotiation-server
client = 322-version-negotiation-client

[322-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[322-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-322]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[323-version-negotiation]
ssl_conf = 323-version-negotiation-ssl

[323-version-negotiation-ssl]
server = 323-version-negotiation-server
client = 323-version-negotiation-client

[323-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[323-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-323]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[324-version-negotiation]
ssl_conf = 324-version-negotiation-ssl

[324-version-negotiation-ssl]
server = 324-version-negotiation-server
client = 324-version-negotiation-client

[324-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[324-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-324]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[325-version-negotiation]
ssl_conf = 325-version-negotiation-ssl

[325-version-negotiation-ssl]
server = 325-version-negotiation-server
client = 325-version-negotiation-client

[325-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[325-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-325]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[326-version-negotiation]
ssl_conf = 326-version-negotiation-ssl

[326-version-negotiation-ssl]
server = 326-version-negotiation-server
client = 326-version-negotiation-client

[326-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[326-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-326]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[327-version-negotiation]
ssl_conf = 327-version-negotiation-ssl

[327-version-negotiation-ssl]
server = 327-version-negotiation-server
client = 327-version-negotiation-client

[327-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[327-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-327]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[328-version-negotiation]
ssl_conf = 328-version-negotiation-ssl

[328-version-negotiation-ssl]
server = 328-version-negotiation-server
client = 328-version-negotiation-client

[328-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[328-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-328]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[329-version-negotiation]
ssl_conf = 329-version-negotiation-ssl

[329-version-negotiation-ssl]
server = 329-version-negotiation-server
client = 329-version-negotiation-client

[329-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[329-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-329]
ExpectedResult = ServerFail


# ===========================================================

[330-version-negotiation]
ssl_conf = 330-version-negotiation-ssl

[330-version-negotiation-ssl]
server = 330-version-negotiation-server
client = 330-version-negotiation-client

[330-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[330-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-330]
ExpectedResult = ServerFail


# ===========================================================

[331-version-negotiation]
ssl_conf = 331-version-negotiation-ssl

[331-version-negotiation-ssl]
server = 331-version-negotiation-server
client = 331-version-negotiation-client

[331-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[331-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-331]
ExpectedResult = ServerFail


# ===========================================================

[332-version-negotiation]
ssl_conf = 332-version-negotiation-ssl

[332-version-negotiation-ssl]
server = 332-version-negotiation-server
client = 332-version-negotiation-client

[332-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[332-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-332]
ExpectedResult = ServerFail


# ===========================================================

[333-version-negotiation]
ssl_conf = 333-version-negotiation-ssl

[333-version-negotiation-ssl]
server = 333-version-negotiation-server
client = 333-version-negotiation-client

[333-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[333-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-333]
ExpectedResult = ServerFail


# ===========================================================

[334-version-negotiation]
ssl_conf = 334-version-negotiation-ssl

[334-version-negotiation-ssl]
server = 334-version-negotiation-server
client = 334-version-negotiation-client

[334-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[334-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-334]
ExpectedResult = ServerFail


# ===========================================================

[335-version-negotiation]
ssl_conf = 335-version-negotiation-ssl

[335-version-negotiation-ssl]
server = 335-version-negotiation-server
client = 335-version-negotiation-client

[335-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[335-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-335]
ExpectedResult = ServerFail


# ===========================================================

[336-version-negotiation]
ssl_conf = 336-version-negotiation-ssl

[336-version-negotiation-ssl]
server = 336-version-negotiation-server
client = 336-version-negotiation-client

[336-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[336-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-336]
ExpectedResult = ServerFail


# ===========================================================

[337-version-negotiation]
ssl_conf = 337-version-negotiation-ssl

[337-version-negotiation-ssl]
server = 337-version-negotiation-server
client = 337-version-negotiation-client

[337-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[337-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-337]
ExpectedResult = ServerFail


# ===========================================================

[338-version-negotiation]
ssl_conf = 338-version-negotiation-ssl

[338-version-negotiation-ssl]
server = 338-version-negotiation-server
client = 338-version-negotiation-client

[338-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[338-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-338]
ExpectedResult = ServerFail


# ===========================================================

[339-version-negotiation]
ssl_conf = 339-version-negotiation-ssl

[339-version-negotiation-ssl]
server = 339-version-negotiation-server
client = 339-version-negotiation-client

[339-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[339-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-339]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[340-version-negotiation]
ssl_conf = 340-version-negotiation-ssl

[340-version-negotiation-ssl]
server = 340-version-negotiation-server
client = 340-version-negotiation-client

[340-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[340-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-340]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[341-version-negotiation]
ssl_conf = 341-version-negotiation-ssl

[341-version-negotiation-ssl]
server = 341-version-negotiation-server
client = 341-version-negotiation-client

[341-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[341-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-341]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[342-version-negotiation]
ssl_conf = 342-version-negotiation-ssl

[342-version-negotiation-ssl]
server = 342-version-negotiation-server
client = 342-version-negotiation-client

[342-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[342-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-342]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[343-version-negotiation]
ssl_conf = 343-version-negotiation-ssl

[343-version-negotiation-ssl]
server = 343-version-negotiation-server
client = 343-version-negotiation-client

[343-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[343-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-343]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[344-version-negotiation]
ssl_conf = 344-version-negotiation-ssl

[344-version-negotiation-ssl]
server = 344-version-negotiation-server
client = 344-version-negotiation-client

[344-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[344-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-344]
ExpectedResult = ServerFail


# ===========================================================

[345-version-negotiation]
ssl_conf = 345-version-negotiation-ssl

[345-version-negotiation-ssl]
server = 345-version-negotiation-server
client = 345-version-negotiation-client

[345-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[345-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-345]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[346-version-negotiation]
ssl_conf = 346-version-negotiation-ssl

[346-version-negotiation-ssl]
server = 346-version-negotiation-server
client = 346-version-negotiation-client

[346-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[346-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-346]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[347-version-negotiation]
ssl_conf = 347-version-negotiation-ssl

[347-version-negotiation-ssl]
server = 347-version-negotiation-server
client = 347-version-negotiation-client

[347-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[347-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-347]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[348-version-negotiation]
ssl_conf = 348-version-negotiation-ssl

[348-version-negotiation-ssl]
server = 348-version-negotiation-server
client = 348-version-negotiation-client

[348-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[348-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-348]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[349-version-negotiation]
ssl_conf = 349-version-negotiation-ssl

[349-version-negotiation-ssl]
server = 349-version-negotiation-server
client = 349-version-negotiation-client

[349-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[349-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-349]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[350-version-negotiation]
ssl_conf = 350-version-negotiation-ssl

[350-version-negotiation-ssl]
server = 350-version-negotiation-server
client = 350-version-negotiation-client

[350-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[350-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-350]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[351-version-negotiation]
ssl_conf = 351-version-negotiation-ssl

[351-version-negotiation-ssl]
server = 351-version-negotiation-server
client = 351-version-negotiation-client

[351-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[351-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-351]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[352-version-negotiation]
ssl_conf = 352-version-negotiation-ssl

[352-version-negotiation-ssl]
server = 352-version-negotiation-server
client = 352-version-negotiation-client

[352-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[352-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-352]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[353-version-negotiation]
ssl_conf = 353-version-negotiation-ssl

[353-version-negotiation-ssl]
server = 353-version-negotiation-server
client = 353-version-negotiation-client

[353-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[353-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-353]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[354-version-negotiation]
ssl_conf = 354-version-negotiation-ssl

[354-version-negotiation-ssl]
server = 354-version-negotiation-server
client = 354-version-negotiation-client

[354-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[354-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-354]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[355-version-negotiation]
ssl_conf = 355-version-negotiation-ssl

[355-version-negotiation-ssl]
server = 355-version-negotiation-server
client = 355-version-negotiation-client

[355-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[355-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-355]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[356-version-negotiation]
ssl_conf = 356-version-negotiation-ssl

[356-version-negotiation-ssl]
server = 356-version-negotiation-server
client = 356-version-negotiation-client

[356-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[356-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-356]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[357-version-negotiation]
ssl_conf = 357-version-negotiation-ssl

[357-version-negotiation-ssl]
server = 357-version-negotiation-server
client = 357-version-negotiation-client

[357-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[357-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-357]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[358-version-negotiation]
ssl_conf = 358-version-negotiation-ssl

[358-version-negotiation-ssl]
server = 358-version-negotiation-server
client = 358-version-negotiation-client

[358-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[358-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-358]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[359-version-negotiation]
ssl_conf = 359-version-negotiation-ssl

[359-version-negotiation-ssl]
server = 359-version-negotiation-server
client = 359-version-negotiation-client

[359-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[359-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-359]
ExpectedResult = ServerFail


# ===========================================================

[360-version-negotiation]
ssl_conf = 360-version-negotiation-ssl

[360-version-negotiation-ssl]
server = 360-version-negotiation-server
client = 360-version-negotiation-client

[360-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[360-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-360]
ExpectedResult = ServerFail


# ===========================================================

[361-version-negotiation]
ssl_conf = 361-version-negotiation-ssl

[361-version-negotiation-ssl]
server = 361-version-negotiation-server
client = 361-version-negotiation-client

[361-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[361-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-361]
ExpectedResult = ServerFail


# ===========================================================

[362-version-negotiation]
ssl_conf = 362-version-negotiation-ssl

[362-version-negotiation-ssl]
server = 362-version-negotiation-server
client = 362-version-negotiation-client

[362-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[362-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-362]
ExpectedResult = ServerFail


# ===========================================================

[363-version-negotiation]
ssl_conf = 363-version-negotiation-ssl

[363-version-negotiation-ssl]
server = 363-version-negotiation-server
client = 363-version-negotiation-client

[363-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[363-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-363]
ExpectedResult = ServerFail


# ===========================================================

[364-version-negotiation]
ssl_conf = 364-version-negotiation-ssl

[364-version-negotiation-ssl]
server = 364-version-negotiation-server
client = 364-version-negotiation-client

[364-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[364-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-364]
ExpectedResult = ServerFail


# ===========================================================

[365-version-negotiation]
ssl_conf = 365-version-negotiation-ssl

[365-version-negotiation-ssl]
server = 365-version-negotiation-server
client = 365-version-negotiation-client

[365-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[365-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-365]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[366-version-negotiation]
ssl_conf = 366-version-negotiation-ssl

[366-version-negotiation-ssl]
server = 366-version-negotiation-server
client = 366-version-negotiation-client

[366-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[366-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-366]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[367-version-negotiation]
ssl_conf = 367-version-negotiation-ssl

[367-version-negotiation-ssl]
server = 367-version-negotiation-server
client = 367-version-negotiation-client

[367-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[367-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-367]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[368-version-negotiation]
ssl_conf = 368-version-negotiation-ssl

[368-version-negotiation-ssl]
server = 368-version-negotiation-server
client = 368-version-negotiation-client

[368-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[368-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-368]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[369-version-negotiation]
ssl_conf = 369-version-negotiation-ssl

[369-version-negotiation-ssl]
server = 369-version-negotiation-server
client = 369-version-negotiation-client

[369-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[369-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-369]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[370-version-negotiation]
ssl_conf = 370-version-negotiation-ssl

[370-version-negotiation-ssl]
server = 370-version-negotiation-server
client = 370-version-negotiation-client

[370-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[370-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-370]
ExpectedResult = ServerFail


# ===========================================================

[371-version-negotiation]
ssl_conf = 371-version-negotiation-ssl

[371-version-negotiation-ssl]
server = 371-version-negotiation-server
client = 371-version-negotiation-client

[371-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[371-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-371]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[372-version-negotiation]
ssl_conf = 372-version-negotiation-ssl

[372-version-negotiation-ssl]
server = 372-version-negotiation-server
client = 372-version-negotiation-client

[372-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[372-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-372]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[373-version-negotiation]
ssl_conf = 373-version-negotiation-ssl

[373-version-negotiation-ssl]
server = 373-version-negotiation-server
client = 373-version-negotiation-client

[373-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[373-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-373]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[374-version-negotiation]
ssl_conf = 374-version-negotiation-ssl

[374-version-negotiation-ssl]
server = 374-version-negotiation-server
client = 374-version-negotiation-client

[374-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[374-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-374]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[375-version-negotiation]
ssl_conf = 375-version-negotiation-ssl

[375-version-negotiation-ssl]
server = 375-version-negotiation-server
client = 375-version-negotiation-client

[375-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[375-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-375]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[376-version-negotiation]
ssl_conf = 376-version-negotiation-ssl

[376-version-negotiation-ssl]
server = 376-version-negotiation-server
client = 376-version-negotiation-client

[376-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[376-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-376]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[377-version-negotiation]
ssl_conf = 377-version-negotiation-ssl

[377-version-negotiation-ssl]
server = 377-version-negotiation-server
client = 377-version-negotiation-client

[377-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[377-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-377]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[378-version-negotiation]
ssl_conf = 378-version-negotiation-ssl

[378-version-negotiation-ssl]
server = 378-version-negotiation-server
client = 378-version-negotiation-client

[378-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[378-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-378]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[379-version-negotiation]
ssl_conf = 379-version-negotiation-ssl

[379-version-negotiation-ssl]
server = 379-version-negotiation-server
client = 379-version-negotiation-client

[379-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[379-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-379]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[380-version-negotiation]
ssl_conf = 380-version-negotiation-ssl

[380-version-negotiation-ssl]
server = 380-version-negotiation-server
client = 380-version-negotiation-client

[380-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[380-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-380]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[381-version-negotiation]
ssl_conf = 381-version-negotiation-ssl

[381-version-negotiation-ssl]
server = 381-version-negotiation-server
client = 381-version-negotiation-client

[381-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[381-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-381]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[382-version-negotiation]
ssl_conf = 382-version-negotiation-ssl

[382-version-negotiation-ssl]
server = 382-version-negotiation-server
client = 382-version-negotiation-client

[382-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[382-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-382]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[383-version-negotiation]
ssl_conf = 383-version-negotiation-ssl

[383-version-negotiation-ssl]
server = 383-version-negotiation-server
client = 383-version-negotiation-client

[383-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[383-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-383]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[384-version-negotiation]
ssl_conf = 384-version-negotiation-ssl

[384-version-negotiation-ssl]
server = 384-version-negotiation-server
client = 384-version-negotiation-client

[384-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[384-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-384]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[385-version-negotiation]
ssl_conf = 385-version-negotiation-ssl

[385-version-negotiation-ssl]
server = 385-version-negotiation-server
client = 385-version-negotiation-client

[385-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[385-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-385]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[386-version-negotiation]
ssl_conf = 386-version-negotiation-ssl

[386-version-negotiation-ssl]
server = 386-version-negotiation-server
client = 386-version-negotiation-client

[386-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[386-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-386]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[387-version-negotiation]
ssl_conf = 387-version-negotiation-ssl

[387-version-negotiation-ssl]
server = 387-version-negotiation-server
client = 387-version-negotiation-client

[387-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[387-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-387]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[388-version-negotiation]
ssl_conf = 388-version-negotiation-ssl

[388-version-negotiation-ssl]
server = 388-version-negotiation-server
client = 388-version-negotiation-client

[388-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[388-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-388]
ExpectedResult = ServerFail


# ===========================================================

[389-version-negotiation]
ssl_conf = 389-version-negotiation-ssl

[389-version-negotiation-ssl]
server = 389-version-negotiation-server
client = 389-version-negotiation-client

[389-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[389-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-389]
ExpectedResult = ServerFail


# ===========================================================

[390-version-negotiation]
ssl_conf = 390-version-negotiation-ssl

[390-version-negotiation-ssl]
server = 390-version-negotiation-server
client = 390-version-negotiation-client

[390-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[390-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-390]
ExpectedResult = ServerFail


# ===========================================================

[391-version-negotiation]
ssl_conf = 391-version-negotiation-ssl

[391-version-negotiation-ssl]
server = 391-version-negotiation-server
client = 391-version-negotiation-client

[391-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[391-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-391]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[392-version-negotiation]
ssl_conf = 392-version-negotiation-ssl

[392-version-negotiation-ssl]
server = 392-version-negotiation-server
client = 392-version-negotiation-client

[392-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[392-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-392]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[393-version-negotiation]
ssl_conf = 393-version-negotiation-ssl

[393-version-negotiation-ssl]
server = 393-version-negotiation-server
client = 393-version-negotiation-client

[393-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[393-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-393]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[394-version-negotiation]
ssl_conf = 394-version-negotiation-ssl

[394-version-negotiation-ssl]
server = 394-version-negotiation-server
client = 394-version-negotiation-client

[394-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[394-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-394]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[395-version-negotiation]
ssl_conf = 395-version-negotiation-ssl

[395-version-negotiation-ssl]
server = 395-version-negotiation-server
client = 395-version-negotiation-client

[395-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[395-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-395]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[396-version-negotiation]
ssl_conf = 396-version-negotiation-ssl

[396-version-negotiation-ssl]
server = 396-version-negotiation-server
client = 396-version-negotiation-client

[396-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[396-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-396]
ExpectedResult = ServerFail


# ===========================================================

[397-version-negotiation]
ssl_conf = 397-version-negotiation-ssl

[397-version-negotiation-ssl]
server = 397-version-negotiation-server
client = 397-version-negotiation-client

[397-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[397-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-397]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[398-version-negotiation]
ssl_conf = 398-version-negotiation-ssl

[398-version-negotiation-ssl]
server = 398-version-negotiation-server
client = 398-version-negotiation-client

[398-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[398-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-398]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[399-version-negotiation]
ssl_conf = 399-version-negotiation-ssl

[399-version-negotiation-ssl]
server = 399-version-negotiation-server
client = 399-version-negotiation-client

[399-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[399-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-399]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[400-version-negotiation]
ssl_conf = 400-version-negotiation-ssl

[400-version-negotiation-ssl]
server = 400-version-negotiation-server
client = 400-version-negotiation-client

[400-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[400-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-400]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[401-version-negotiation]
ssl_conf = 401-version-negotiation-ssl

[401-version-negotiation-ssl]
server = 401-version-negotiation-server
client = 401-version-negotiation-client

[401-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[401-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-401]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[402-version-negotiation]
ssl_conf = 402-version-negotiation-ssl

[402-version-negotiation-ssl]
server = 402-version-negotiation-server
client = 402-version-negotiation-client

[402-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[402-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-402]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[403-version-negotiation]
ssl_conf = 403-version-negotiation-ssl

[403-version-negotiation-ssl]
server = 403-version-negotiation-server
client = 403-version-negotiation-client

[403-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[403-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-403]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[404-version-negotiation]
ssl_conf = 404-version-negotiation-ssl

[404-version-negotiation-ssl]
server = 404-version-negotiation-server
client = 404-version-negotiation-client

[404-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[404-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-404]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[405-version-negotiation]
ssl_conf = 405-version-negotiation-ssl

[405-version-negotiation-ssl]
server = 405-version-negotiation-server
client = 405-version-negotiation-client

[405-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[405-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-405]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[406-version-negotiation]
ssl_conf = 406-version-negotiation-ssl

[406-version-negotiation-ssl]
server = 406-version-negotiation-server
client = 406-version-negotiation-client

[406-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[406-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-406]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[407-version-negotiation]
ssl_conf = 407-version-negotiation-ssl

[407-version-negotiation-ssl]
server = 407-version-negotiation-server
client = 407-version-negotiation-client

[407-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[407-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-407]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[408-version-negotiation]
ssl_conf = 408-version-negotiation-ssl

[408-version-negotiation-ssl]
server = 408-version-negotiation-server
client = 408-version-negotiation-client

[408-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[408-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-408]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[409-version-negotiation]
ssl_conf = 409-version-negotiation-ssl

[409-version-negotiation-ssl]
server = 409-version-negotiation-server
client = 409-version-negotiation-client

[409-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[409-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-409]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[410-version-negotiation]
ssl_conf = 410-version-negotiation-ssl

[410-version-negotiation-ssl]
server = 410-version-negotiation-server
client = 410-version-negotiation-client

[410-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[410-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-410]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[411-version-negotiation]
ssl_conf = 411-version-negotiation-ssl

[411-version-negotiation-ssl]
server = 411-version-negotiation-server
client = 411-version-negotiation-client

[411-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[411-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-411]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[412-version-negotiation]
ssl_conf = 412-version-negotiation-ssl

[412-version-negotiation-ssl]
server = 412-version-negotiation-server
client = 412-version-negotiation-client

[412-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[412-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-412]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[413-version-negotiation]
ssl_conf = 413-version-negotiation-ssl

[413-version-negotiation-ssl]
server = 413-version-negotiation-server
client = 413-version-negotiation-client

[413-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[413-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-413]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[414-version-negotiation]
ssl_conf = 414-version-negotiation-ssl

[414-version-negotiation-ssl]
server = 414-version-negotiation-server
client = 414-version-negotiation-client

[414-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[414-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-414]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[415-version-negotiation]
ssl_conf = 415-version-negotiation-ssl

[415-version-negotiation-ssl]
server = 415-version-negotiation-server
client = 415-version-negotiation-client

[415-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[415-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-415]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[416-version-negotiation]
ssl_conf = 416-version-negotiation-ssl

[416-version-negotiation-ssl]
server = 416-version-negotiation-server
client = 416-version-negotiation-client

[416-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[416-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-416]
ExpectedResult = ServerFail


# ===========================================================

[417-version-negotiation]
ssl_conf = 417-version-negotiation-ssl

[417-version-negotiation-ssl]
server = 417-version-negotiation-server
client = 417-version-negotiation-client

[417-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[417-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-417]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[418-version-negotiation]
ssl_conf = 418-version-negotiation-ssl

[418-version-negotiation-ssl]
server = 418-version-negotiation-server
client = 418-version-negotiation-client

[418-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[418-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-418]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[419-version-negotiation]
ssl_conf = 419-version-negotiation-ssl

[419-version-negotiation-ssl]
server = 419-version-negotiation-server
client = 419-version-negotiation-client

[419-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[419-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-419]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[420-version-negotiation]
ssl_conf = 420-version-negotiation-ssl

[420-version-negotiation-ssl]
server = 420-version-negotiation-server
client = 420-version-negotiation-client

[420-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[420-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-420]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[421-version-negotiation]
ssl_conf = 421-version-negotiation-ssl

[421-version-negotiation-ssl]
server = 421-version-negotiation-server
client = 421-version-negotiation-client

[421-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[421-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-421]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[422-version-negotiation]
ssl_conf = 422-version-negotiation-ssl

[422-version-negotiation-ssl]
server = 422-version-negotiation-server
client = 422-version-negotiation-client

[422-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[422-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-422]
ExpectedResult = ServerFail


# ===========================================================

[423-version-negotiation]
ssl_conf = 423-version-negotiation-ssl

[423-version-negotiation-ssl]
server = 423-version-negotiation-server
client = 423-version-negotiation-client

[423-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[423-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-423]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[424-version-negotiation]
ssl_conf = 424-version-negotiation-ssl

[424-version-negotiation-ssl]
server = 424-version-negotiation-server
client = 424-version-negotiation-client

[424-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[424-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-424]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[425-version-negotiation]
ssl_conf = 425-version-negotiation-ssl

[425-version-negotiation-ssl]
server = 425-version-negotiation-server
client = 425-version-negotiation-client

[425-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[425-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-425]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[426-version-negotiation]
ssl_conf = 426-version-negotiation-ssl

[426-version-negotiation-ssl]
server = 426-version-negotiation-server
client = 426-version-negotiation-client

[426-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[426-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-426]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[427-version-negotiation]
ssl_conf = 427-version-negotiation-ssl

[427-version-negotiation-ssl]
server = 427-version-negotiation-server
client = 427-version-negotiation-client

[427-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[427-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-427]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[428-version-negotiation]
ssl_conf = 428-version-negotiation-ssl

[428-version-negotiation-ssl]
server = 428-version-negotiation-server
client = 428-version-negotiation-client

[428-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[428-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-428]
ExpectedProtocol = TLSv1
ExpectedResult = Success


# ===========================================================

[429-version-negotiation]
ssl_conf = 429-version-negotiation-ssl

[429-version-negotiation-ssl]
server = 429-version-negotiation-server
client = 429-version-negotiation-client

[429-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[429-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-429]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[430-version-negotiation]
ssl_conf = 430-version-negotiation-ssl

[430-version-negotiation-ssl]
server = 430-version-negotiation-server
client = 430-version-negotiation-client

[430-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[430-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-430]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[431-version-negotiation]
ssl_conf = 431-version-negotiation-ssl

[431-version-negotiation-ssl]
server = 431-version-negotiation-server
client = 431-version-negotiation-client

[431-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[431-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-431]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[432-version-negotiation]
ssl_conf = 432-version-negotiation-ssl

[432-version-negotiation-ssl]
server = 432-version-negotiation-server
client = 432-version-negotiation-client

[432-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[432-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-432]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[433-version-negotiation]
ssl_conf = 433-version-negotiation-ssl

[433-version-negotiation-ssl]
server = 433-version-negotiation-server
client = 433-version-negotiation-client

[433-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[433-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-433]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[434-version-negotiation]
ssl_conf = 434-version-negotiation-ssl

[434-version-negotiation-ssl]
server = 434-version-negotiation-server
client = 434-version-negotiation-client

[434-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[434-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-434]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[435-version-negotiation]
ssl_conf = 435-version-negotiation-ssl

[435-version-negotiation-ssl]
server = 435-version-negotiation-server
client = 435-version-negotiation-client

[435-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[435-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-435]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[436-version-negotiation]
ssl_conf = 436-version-negotiation-ssl

[436-version-negotiation-ssl]
server = 436-version-negotiation-server
client = 436-version-negotiation-client

[436-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[436-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-436]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[437-version-negotiation]
ssl_conf = 437-version-negotiation-ssl

[437-version-negotiation-ssl]
server = 437-version-negotiation-server
client = 437-version-negotiation-client

[437-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[437-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-437]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[438-version-negotiation]
ssl_conf = 438-version-negotiation-ssl

[438-version-negotiation-ssl]
server = 438-version-negotiation-server
client = 438-version-negotiation-client

[438-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[438-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-438]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[439-version-negotiation]
ssl_conf = 439-version-negotiation-ssl

[439-version-negotiation-ssl]
server = 439-version-negotiation-server
client = 439-version-negotiation-client

[439-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[439-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-439]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[440-version-negotiation]
ssl_conf = 440-version-negotiation-ssl

[440-version-negotiation-ssl]
server = 440-version-negotiation-server
client = 440-version-negotiation-client

[440-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[440-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-440]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[441-version-negotiation]
ssl_conf = 441-version-negotiation-ssl

[441-version-negotiation-ssl]
server = 441-version-negotiation-server
client = 441-version-negotiation-client

[441-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[441-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-441]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[442-version-negotiation]
ssl_conf = 442-version-negotiation-ssl

[442-version-negotiation-ssl]
server = 442-version-negotiation-server
client = 442-version-negotiation-client

[442-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[442-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-442]
ExpectedResult = ServerFail


# ===========================================================

[443-version-negotiation]
ssl_conf = 443-version-negotiation-ssl

[443-version-negotiation-ssl]
server = 443-version-negotiation-server
client = 443-version-negotiation-client

[443-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[443-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-443]
ExpectedResult = ClientFail


# ===========================================================

[444-version-negotiation]
ssl_conf = 444-version-negotiation-ssl

[444-version-negotiation-ssl]
server = 444-version-negotiation-server
client = 444-version-negotiation-client

[444-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[444-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-444]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[445-version-negotiation]
ssl_conf = 445-version-negotiation-ssl

[445-version-negotiation-ssl]
server = 445-version-negotiation-server
client = 445-version-negotiation-client

[445-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[445-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-445]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[446-version-negotiation]
ssl_conf = 446-version-negotiation-ssl

[446-version-negotiation-ssl]
server = 446-version-negotiation-server
client = 446-version-negotiation-client

[446-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[446-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-446]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[447-version-negotiation]
ssl_conf = 447-version-negotiation-ssl

[447-version-negotiation-ssl]
server = 447-version-negotiation-server
client = 447-version-negotiation-client

[447-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[447-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-447]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[448-version-negotiation]
ssl_conf = 448-version-negotiation-ssl

[448-version-negotiation-ssl]
server = 448-version-negotiation-server
client = 448-version-negotiation-client

[448-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[448-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-448]
ExpectedResult = ServerFail


# ===========================================================

[449-version-negotiation]
ssl_conf = 449-version-negotiation-ssl

[449-version-negotiation-ssl]
server = 449-version-negotiation-server
client = 449-version-negotiation-client

[449-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[449-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-449]
ExpectedResult = ClientFail


# ===========================================================

[450-version-negotiation]
ssl_conf = 450-version-negotiation-ssl

[450-version-negotiation-ssl]
server = 450-version-negotiation-server
client = 450-version-negotiation-client

[450-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[450-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-450]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[451-version-negotiation]
ssl_conf = 451-version-negotiation-ssl

[451-version-negotiation-ssl]
server = 451-version-negotiation-server
client = 451-version-negotiation-client

[451-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[451-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-451]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[452-version-negotiation]
ssl_conf = 452-version-negotiation-ssl

[452-version-negotiation-ssl]
server = 452-version-negotiation-server
client = 452-version-negotiation-client

[452-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[452-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-452]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[453-version-negotiation]
ssl_conf = 453-version-negotiation-ssl

[453-version-negotiation-ssl]
server = 453-version-negotiation-server
client = 453-version-negotiation-client

[453-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[453-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-453]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[454-version-negotiation]
ssl_conf = 454-version-negotiation-ssl

[454-version-negotiation-ssl]
server = 454-version-negotiation-server
client = 454-version-negotiation-client

[454-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[454-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-454]
ExpectedResult = ClientFail


# ===========================================================

[455-version-negotiation]
ssl_conf = 455-version-negotiation-ssl

[455-version-negotiation-ssl]
server = 455-version-negotiation-server
client = 455-version-negotiation-client

[455-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[455-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-455]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[456-version-negotiation]
ssl_conf = 456-version-negotiation-ssl

[456-version-negotiation-ssl]
server = 456-version-negotiation-server
client = 456-version-negotiation-client

[456-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[456-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-456]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[457-version-negotiation]
ssl_conf = 457-version-negotiation-ssl

[457-version-negotiation-ssl]
server = 457-version-negotiation-server
client = 457-version-negotiation-client

[457-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[457-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-457]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[458-version-negotiation]
ssl_conf = 458-version-negotiation-ssl

[458-version-negotiation-ssl]
server = 458-version-negotiation-server
client = 458-version-negotiation-client

[458-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[458-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-458]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[459-version-negotiation]
ssl_conf = 459-version-negotiation-ssl

[459-version-negotiation-ssl]
server = 459-version-negotiation-server
client = 459-version-negotiation-client

[459-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[459-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-459]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[460-version-negotiation]
ssl_conf = 460-version-negotiation-ssl

[460-version-negotiation-ssl]
server = 460-version-negotiation-server
client = 460-version-negotiation-client

[460-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[460-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-460]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[461-version-negotiation]
ssl_conf = 461-version-negotiation-ssl

[461-version-negotiation-ssl]
server = 461-version-negotiation-server
client = 461-version-negotiation-client

[461-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[461-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-461]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[462-version-negotiation]
ssl_conf = 462-version-negotiation-ssl

[462-version-negotiation-ssl]
server = 462-version-negotiation-server
client = 462-version-negotiation-client

[462-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[462-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-462]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[463-version-negotiation]
ssl_conf = 463-version-negotiation-ssl

[463-version-negotiation-ssl]
server = 463-version-negotiation-server
client = 463-version-negotiation-client

[463-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[463-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-463]
ExpectedResult = ServerFail


# ===========================================================

[464-version-negotiation]
ssl_conf = 464-version-negotiation-ssl

[464-version-negotiation-ssl]
server = 464-version-negotiation-server
client = 464-version-negotiation-client

[464-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[464-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-464]
ExpectedResult = ServerFail


# ===========================================================

[465-version-negotiation]
ssl_conf = 465-version-negotiation-ssl

[465-version-negotiation-ssl]
server = 465-version-negotiation-server
client = 465-version-negotiation-client

[465-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[465-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-465]
ExpectedResult = ServerFail


# ===========================================================

[466-version-negotiation]
ssl_conf = 466-version-negotiation-ssl

[466-version-negotiation-ssl]
server = 466-version-negotiation-server
client = 466-version-negotiation-client

[466-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[466-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-466]
ExpectedResult = ServerFail


# ===========================================================

[467-version-negotiation]
ssl_conf = 467-version-negotiation-ssl

[467-version-negotiation-ssl]
server = 467-version-negotiation-server
client = 467-version-negotiation-client

[467-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[467-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-467]
ExpectedResult = ServerFail


# ===========================================================

[468-version-negotiation]
ssl_conf = 468-version-negotiation-ssl

[468-version-negotiation-ssl]
server = 468-version-negotiation-server
client = 468-version-negotiation-client

[468-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[468-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-468]
ExpectedResult = ServerFail


# ===========================================================

[469-version-negotiation]
ssl_conf = 469-version-negotiation-ssl

[469-version-negotiation-ssl]
server = 469-version-negotiation-server
client = 469-version-negotiation-client

[469-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[469-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-469]
ExpectedResult = ClientFail


# ===========================================================

[470-version-negotiation]
ssl_conf = 470-version-negotiation-ssl

[470-version-negotiation-ssl]
server = 470-version-negotiation-server
client = 470-version-negotiation-client

[470-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[470-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-470]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[471-version-negotiation]
ssl_conf = 471-version-negotiation-ssl

[471-version-negotiation-ssl]
server = 471-version-negotiation-server
client = 471-version-negotiation-client

[471-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[471-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-471]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[472-version-negotiation]
ssl_conf = 472-version-negotiation-ssl

[472-version-negotiation-ssl]
server = 472-version-negotiation-server
client = 472-version-negotiation-client

[472-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[472-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-472]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[473-version-negotiation]
ssl_conf = 473-version-negotiation-ssl

[473-version-negotiation-ssl]
server = 473-version-negotiation-server
client = 473-version-negotiation-client

[473-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[473-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-473]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[474-version-negotiation]
ssl_conf = 474-version-negotiation-ssl

[474-version-negotiation-ssl]
server = 474-version-negotiation-server
client = 474-version-negotiation-client

[474-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[474-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-474]
ExpectedResult = ServerFail


# ===========================================================

[475-version-negotiation]
ssl_conf = 475-version-negotiation-ssl

[475-version-negotiation-ssl]
server = 475-version-negotiation-server
client = 475-version-negotiation-client

[475-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[475-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-475]
ExpectedResult = ClientFail


# ===========================================================

[476-version-negotiation]
ssl_conf = 476-version-negotiation-ssl

[476-version-negotiation-ssl]
server = 476-version-negotiation-server
client = 476-version-negotiation-client

[476-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[476-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-476]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[477-version-negotiation]
ssl_conf = 477-version-negotiation-ssl

[477-version-negotiation-ssl]
server = 477-version-negotiation-server
client = 477-version-negotiation-client

[477-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[477-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-477]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[478-version-negotiation]
ssl_conf = 478-version-negotiation-ssl

[478-version-negotiation-ssl]
server = 478-version-negotiation-server
client = 478-version-negotiation-client

[478-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[478-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-478]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[479-version-negotiation]
ssl_conf = 479-version-negotiation-ssl

[479-version-negotiation-ssl]
server = 479-version-negotiation-server
client = 479-version-negotiation-client

[479-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[479-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-479]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[480-version-negotiation]
ssl_conf = 480-version-negotiation-ssl

[480-version-negotiation-ssl]
server = 480-version-negotiation-server
client = 480-version-negotiation-client

[480-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[480-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-480]
ExpectedResult = ClientFail


# ===========================================================

[481-version-negotiation]
ssl_conf = 481-version-negotiation-ssl

[481-version-negotiation-ssl]
server = 481-version-negotiation-server
client = 481-version-negotiation-client

[481-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[481-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-481]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[482-version-negotiation]
ssl_conf = 482-version-negotiation-ssl

[482-version-negotiation-ssl]
server = 482-version-negotiation-server
client = 482-version-negotiation-client

[482-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[482-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-482]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[483-version-negotiation]
ssl_conf = 483-version-negotiation-ssl

[483-version-negotiation-ssl]
server = 483-version-negotiation-server
client = 483-version-negotiation-client

[483-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[483-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-483]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[484-version-negotiation]
ssl_conf = 484-version-negotiation-ssl

[484-version-negotiation-ssl]
server = 484-version-negotiation-server
client = 484-version-negotiation-client

[484-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[484-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-484]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[485-version-negotiation]
ssl_conf = 485-version-negotiation-ssl

[485-version-negotiation-ssl]
server = 485-version-negotiation-server
client = 485-version-negotiation-client

[485-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[485-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-485]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[486-version-negotiation]
ssl_conf = 486-version-negotiation-ssl

[486-version-negotiation-ssl]
server = 486-version-negotiation-server
client = 486-version-negotiation-client

[486-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[486-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-486]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[487-version-negotiation]
ssl_conf = 487-version-negotiation-ssl

[487-version-negotiation-ssl]
server = 487-version-negotiation-server
client = 487-version-negotiation-client

[487-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[487-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-487]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[488-version-negotiation]
ssl_conf = 488-version-negotiation-ssl

[488-version-negotiation-ssl]
server = 488-version-negotiation-server
client = 488-version-negotiation-client

[488-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[488-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-488]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[489-version-negotiation]
ssl_conf = 489-version-negotiation-ssl

[489-version-negotiation-ssl]
server = 489-version-negotiation-server
client = 489-version-negotiation-client

[489-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[489-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-489]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[490-version-negotiation]
ssl_conf = 490-version-negotiation-ssl

[490-version-negotiation-ssl]
server = 490-version-negotiation-server
client = 490-version-negotiation-client

[490-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[490-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-490]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[491-version-negotiation]
ssl_conf = 491-version-negotiation-ssl

[491-version-negotiation-ssl]
server = 491-version-negotiation-server
client = 491-version-negotiation-client

[491-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[491-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-491]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[492-version-negotiation]
ssl_conf = 492-version-negotiation-ssl

[492-version-negotiation-ssl]
server = 492-version-negotiation-server
client = 492-version-negotiation-client

[492-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[492-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-492]
ExpectedResult = ServerFail


# ===========================================================

[493-version-negotiation]
ssl_conf = 493-version-negotiation-ssl

[493-version-negotiation-ssl]
server = 493-version-negotiation-server
client = 493-version-negotiation-client

[493-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[493-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-493]
ExpectedResult = ServerFail


# ===========================================================

[494-version-negotiation]
ssl_conf = 494-version-negotiation-ssl

[494-version-negotiation-ssl]
server = 494-version-negotiation-server
client = 494-version-negotiation-client

[494-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[494-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-494]
ExpectedResult = ServerFail


# ===========================================================

[495-version-negotiation]
ssl_conf = 495-version-negotiation-ssl

[495-version-negotiation-ssl]
server = 495-version-negotiation-server
client = 495-version-negotiation-client

[495-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[495-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-495]
ExpectedResult = ServerFail


# ===========================================================

[496-version-negotiation]
ssl_conf = 496-version-negotiation-ssl

[496-version-negotiation-ssl]
server = 496-version-negotiation-server
client = 496-version-negotiation-client

[496-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[496-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-496]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[497-version-negotiation]
ssl_conf = 497-version-negotiation-ssl

[497-version-negotiation-ssl]
server = 497-version-negotiation-server
client = 497-version-negotiation-client

[497-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[497-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-497]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[498-version-negotiation]
ssl_conf = 498-version-negotiation-ssl

[498-version-negotiation-ssl]
server = 498-version-negotiation-server
client = 498-version-negotiation-client

[498-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[498-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-498]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[499-version-negotiation]
ssl_conf = 499-version-negotiation-ssl

[499-version-negotiation-ssl]
server = 499-version-negotiation-server
client = 499-version-negotiation-client

[499-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[499-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-499]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[500-version-negotiation]
ssl_conf = 500-version-negotiation-ssl

[500-version-negotiation-ssl]
server = 500-version-negotiation-server
client = 500-version-negotiation-client

[500-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[500-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-500]
ExpectedResult = ServerFail


# ===========================================================

[501-version-negotiation]
ssl_conf = 501-version-negotiation-ssl

[501-version-negotiation-ssl]
server = 501-version-negotiation-server
client = 501-version-negotiation-client

[501-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[501-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-501]
ExpectedResult = ServerFail


# ===========================================================

[502-version-negotiation]
ssl_conf = 502-version-negotiation-ssl

[502-version-negotiation-ssl]
server = 502-version-negotiation-server
client = 502-version-negotiation-client

[502-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[502-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-502]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[503-version-negotiation]
ssl_conf = 503-version-negotiation-ssl

[503-version-negotiation-ssl]
server = 503-version-negotiation-server
client = 503-version-negotiation-client

[503-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[503-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-503]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[504-version-negotiation]
ssl_conf = 504-version-negotiation-ssl

[504-version-negotiation-ssl]
server = 504-version-negotiation-server
client = 504-version-negotiation-client

[504-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[504-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-504]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[505-version-negotiation]
ssl_conf = 505-version-negotiation-ssl

[505-version-negotiation-ssl]
server = 505-version-negotiation-server
client = 505-version-negotiation-client

[505-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[505-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-505]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[506-version-negotiation]
ssl_conf = 506-version-negotiation-ssl

[506-version-negotiation-ssl]
server = 506-version-negotiation-server
client = 506-version-negotiation-client

[506-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[506-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-506]
ExpectedResult = ServerFail


# ===========================================================

[507-version-negotiation]
ssl_conf = 507-version-negotiation-ssl

[507-version-negotiation-ssl]
server = 507-version-negotiation-server
client = 507-version-negotiation-client

[507-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[507-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-507]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[508-version-negotiation]
ssl_conf = 508-version-negotiation-ssl

[508-version-negotiation-ssl]
server = 508-version-negotiation-server
client = 508-version-negotiation-client

[508-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[508-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-508]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[509-version-negotiation]
ssl_conf = 509-version-negotiation-ssl

[509-version-negotiation-ssl]
server = 509-version-negotiation-server
client = 509-version-negotiation-client

[509-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[509-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-509]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[510-version-negotiation]
ssl_conf = 510-version-negotiation-ssl

[510-version-negotiation-ssl]
server = 510-version-negotiation-server
client = 510-version-negotiation-client

[510-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[510-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-510]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[511-version-negotiation]
ssl_conf = 511-version-negotiation-ssl

[511-version-negotiation-ssl]
server = 511-version-negotiation-server
client = 511-version-negotiation-client

[511-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[511-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-511]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[512-version-negotiation]
ssl_conf = 512-version-negotiation-ssl

[512-version-negotiation-ssl]
server = 512-version-negotiation-server
client = 512-version-negotiation-client

[512-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[512-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-512]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[513-version-negotiation]
ssl_conf = 513-version-negotiation-ssl

[513-version-negotiation-ssl]
server = 513-version-negotiation-server
client = 513-version-negotiation-client

[513-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[513-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-513]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[514-version-negotiation]
ssl_conf = 514-version-negotiation-ssl

[514-version-negotiation-ssl]
server = 514-version-negotiation-server
client = 514-version-negotiation-client

[514-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[514-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-514]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[515-version-negotiation]
ssl_conf = 515-version-negotiation-ssl

[515-version-negotiation-ssl]
server = 515-version-negotiation-server
client = 515-version-negotiation-client

[515-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[515-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-515]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[516-version-negotiation]
ssl_conf = 516-version-negotiation-ssl

[516-version-negotiation-ssl]
server = 516-version-negotiation-server
client = 516-version-negotiation-client

[516-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[516-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-516]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[517-version-negotiation]
ssl_conf = 517-version-negotiation-ssl

[517-version-negotiation-ssl]
server = 517-version-negotiation-server
client = 517-version-negotiation-client

[517-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[517-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-517]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[518-version-negotiation]
ssl_conf = 518-version-negotiation-ssl

[518-version-negotiation-ssl]
server = 518-version-negotiation-server
client = 518-version-negotiation-client

[518-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[518-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-518]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[519-version-negotiation]
ssl_conf = 519-version-negotiation-ssl

[519-version-negotiation-ssl]
server = 519-version-negotiation-server
client = 519-version-negotiation-client

[519-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[519-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-519]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[520-version-negotiation]
ssl_conf = 520-version-negotiation-ssl

[520-version-negotiation-ssl]
server = 520-version-negotiation-server
client = 520-version-negotiation-client

[520-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[520-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-520]
ExpectedResult = ServerFail


# ===========================================================

[521-version-negotiation]
ssl_conf = 521-version-negotiation-ssl

[521-version-negotiation-ssl]
server = 521-version-negotiation-server
client = 521-version-negotiation-client

[521-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[521-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-521]
ExpectedResult = ServerFail


# ===========================================================

[522-version-negotiation]
ssl_conf = 522-version-negotiation-ssl

[522-version-negotiation-ssl]
server = 522-version-negotiation-server
client = 522-version-negotiation-client

[522-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[522-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-522]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[523-version-negotiation]
ssl_conf = 523-version-negotiation-ssl

[523-version-negotiation-ssl]
server = 523-version-negotiation-server
client = 523-version-negotiation-client

[523-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[523-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-523]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[524-version-negotiation]
ssl_conf = 524-version-negotiation-ssl

[524-version-negotiation-ssl]
server = 524-version-negotiation-server
client = 524-version-negotiation-client

[524-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[524-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-524]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[525-version-negotiation]
ssl_conf = 525-version-negotiation-ssl

[525-version-negotiation-ssl]
server = 525-version-negotiation-server
client = 525-version-negotiation-client

[525-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[525-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-525]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[526-version-negotiation]
ssl_conf = 526-version-negotiation-ssl

[526-version-negotiation-ssl]
server = 526-version-negotiation-server
client = 526-version-negotiation-client

[526-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[526-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-526]
ExpectedResult = ServerFail


# ===========================================================

[527-version-negotiation]
ssl_conf = 527-version-negotiation-ssl

[527-version-negotiation-ssl]
server = 527-version-negotiation-server
client = 527-version-negotiation-client

[527-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[527-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-527]
ExpectedResult = ServerFail


# ===========================================================

[528-version-negotiation]
ssl_conf = 528-version-negotiation-ssl

[528-version-negotiation-ssl]
server = 528-version-negotiation-server
client = 528-version-negotiation-client

[528-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[528-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-528]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[529-version-negotiation]
ssl_conf = 529-version-negotiation-ssl

[529-version-negotiation-ssl]
server = 529-version-negotiation-server
client = 529-version-negotiation-client

[529-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[529-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-529]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[530-version-negotiation]
ssl_conf = 530-version-negotiation-ssl

[530-version-negotiation-ssl]
server = 530-version-negotiation-server
client = 530-version-negotiation-client

[530-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[530-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-530]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[531-version-negotiation]
ssl_conf = 531-version-negotiation-ssl

[531-version-negotiation-ssl]
server = 531-version-negotiation-server
client = 531-version-negotiation-client

[531-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[531-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-531]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[532-version-negotiation]
ssl_conf = 532-version-negotiation-ssl

[532-version-negotiation-ssl]
server = 532-version-negotiation-server
client = 532-version-negotiation-client

[532-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[532-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-532]
ExpectedResult = ServerFail


# ===========================================================

[533-version-negotiation]
ssl_conf = 533-version-negotiation-ssl

[533-version-negotiation-ssl]
server = 533-version-negotiation-server
client = 533-version-negotiation-client

[533-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[533-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-533]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[534-version-negotiation]
ssl_conf = 534-version-negotiation-ssl

[534-version-negotiation-ssl]
server = 534-version-negotiation-server
client = 534-version-negotiation-client

[534-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[534-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-534]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[535-version-negotiation]
ssl_conf = 535-version-negotiation-ssl

[535-version-negotiation-ssl]
server = 535-version-negotiation-server
client = 535-version-negotiation-client

[535-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[535-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-535]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[536-version-negotiation]
ssl_conf = 536-version-negotiation-ssl

[536-version-negotiation-ssl]
server = 536-version-negotiation-server
client = 536-version-negotiation-client

[536-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[536-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-536]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[537-version-negotiation]
ssl_conf = 537-version-negotiation-ssl

[537-version-negotiation-ssl]
server = 537-version-negotiation-server
client = 537-version-negotiation-client

[537-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[537-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-537]
ExpectedProtocol = TLSv1.1
ExpectedResult = Success


# ===========================================================

[538-version-negotiation]
ssl_conf = 538-version-negotiation-ssl

[538-version-negotiation-ssl]
server = 538-version-negotiation-server
client = 538-version-negotiation-client

[538-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[538-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-538]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[539-version-negotiation]
ssl_conf = 539-version-negotiation-ssl

[539-version-negotiation-ssl]
server = 539-version-negotiation-server
client = 539-version-negotiation-client

[539-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[539-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-539]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[540-version-negotiation]
ssl_conf = 540-version-negotiation-ssl

[540-version-negotiation-ssl]
server = 540-version-negotiation-server
client = 540-version-negotiation-client

[540-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[540-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-540]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[541-version-negotiation]
ssl_conf = 541-version-negotiation-ssl

[541-version-negotiation-ssl]
server = 541-version-negotiation-server
client = 541-version-negotiation-client

[541-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[541-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-541]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[542-version-negotiation]
ssl_conf = 542-version-negotiation-ssl

[542-version-negotiation-ssl]
server = 542-version-negotiation-server
client = 542-version-negotiation-client

[542-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[542-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-542]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[543-version-negotiation]
ssl_conf = 543-version-negotiation-ssl

[543-version-negotiation-ssl]
server = 543-version-negotiation-server
client = 543-version-negotiation-client

[543-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[543-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-543]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[544-version-negotiation]
ssl_conf = 544-version-negotiation-ssl

[544-version-negotiation-ssl]
server = 544-version-negotiation-server
client = 544-version-negotiation-client

[544-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[544-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-544]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[545-version-negotiation]
ssl_conf = 545-version-negotiation-ssl

[545-version-negotiation-ssl]
server = 545-version-negotiation-server
client = 545-version-negotiation-client

[545-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[545-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-545]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[546-version-negotiation]
ssl_conf = 546-version-negotiation-ssl

[546-version-negotiation-ssl]
server = 546-version-negotiation-server
client = 546-version-negotiation-client

[546-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[546-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-546]
ExpectedResult = ServerFail


# ===========================================================

[547-version-negotiation]
ssl_conf = 547-version-negotiation-ssl

[547-version-negotiation-ssl]
server = 547-version-negotiation-server
client = 547-version-negotiation-client

[547-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[547-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-547]
ExpectedResult = ClientFail


# ===========================================================

[548-version-negotiation]
ssl_conf = 548-version-negotiation-ssl

[548-version-negotiation-ssl]
server = 548-version-negotiation-server
client = 548-version-negotiation-client

[548-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[548-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-548]
ExpectedResult = ClientFail


# ===========================================================

[549-version-negotiation]
ssl_conf = 549-version-negotiation-ssl

[549-version-negotiation-ssl]
server = 549-version-negotiation-server
client = 549-version-negotiation-client

[549-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[549-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-549]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[550-version-negotiation]
ssl_conf = 550-version-negotiation-ssl

[550-version-negotiation-ssl]
server = 550-version-negotiation-server
client = 550-version-negotiation-client

[550-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[550-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-550]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[551-version-negotiation]
ssl_conf = 551-version-negotiation-ssl

[551-version-negotiation-ssl]
server = 551-version-negotiation-server
client = 551-version-negotiation-client

[551-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[551-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-551]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[552-version-negotiation]
ssl_conf = 552-version-negotiation-ssl

[552-version-negotiation-ssl]
server = 552-version-negotiation-server
client = 552-version-negotiation-client

[552-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[552-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-552]
ExpectedResult = ServerFail


# ===========================================================

[553-version-negotiation]
ssl_conf = 553-version-negotiation-ssl

[553-version-negotiation-ssl]
server = 553-version-negotiation-server
client = 553-version-negotiation-client

[553-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[553-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-553]
ExpectedResult = ClientFail


# ===========================================================

[554-version-negotiation]
ssl_conf = 554-version-negotiation-ssl

[554-version-negotiation-ssl]
server = 554-version-negotiation-server
client = 554-version-negotiation-client

[554-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[554-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-554]
ExpectedResult = ClientFail


# ===========================================================

[555-version-negotiation]
ssl_conf = 555-version-negotiation-ssl

[555-version-negotiation-ssl]
server = 555-version-negotiation-server
client = 555-version-negotiation-client

[555-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[555-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-555]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[556-version-negotiation]
ssl_conf = 556-version-negotiation-ssl

[556-version-negotiation-ssl]
server = 556-version-negotiation-server
client = 556-version-negotiation-client

[556-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[556-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-556]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[557-version-negotiation]
ssl_conf = 557-version-negotiation-ssl

[557-version-negotiation-ssl]
server = 557-version-negotiation-server
client = 557-version-negotiation-client

[557-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[557-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-557]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[558-version-negotiation]
ssl_conf = 558-version-negotiation-ssl

[558-version-negotiation-ssl]
server = 558-version-negotiation-server
client = 558-version-negotiation-client

[558-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[558-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-558]
ExpectedResult = ClientFail


# ===========================================================

[559-version-negotiation]
ssl_conf = 559-version-negotiation-ssl

[559-version-negotiation-ssl]
server = 559-version-negotiation-server
client = 559-version-negotiation-client

[559-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[559-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-559]
ExpectedResult = ClientFail


# ===========================================================

[560-version-negotiation]
ssl_conf = 560-version-negotiation-ssl

[560-version-negotiation-ssl]
server = 560-version-negotiation-server
client = 560-version-negotiation-client

[560-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[560-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-560]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[561-version-negotiation]
ssl_conf = 561-version-negotiation-ssl

[561-version-negotiation-ssl]
server = 561-version-negotiation-server
client = 561-version-negotiation-client

[561-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[561-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-561]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[562-version-negotiation]
ssl_conf = 562-version-negotiation-ssl

[562-version-negotiation-ssl]
server = 562-version-negotiation-server
client = 562-version-negotiation-client

[562-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[562-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-562]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[563-version-negotiation]
ssl_conf = 563-version-negotiation-ssl

[563-version-negotiation-ssl]
server = 563-version-negotiation-server
client = 563-version-negotiation-client

[563-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[563-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-563]
ExpectedResult = ClientFail


# ===========================================================

[564-version-negotiation]
ssl_conf = 564-version-negotiation-ssl

[564-version-negotiation-ssl]
server = 564-version-negotiation-server
client = 564-version-negotiation-client

[564-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[564-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-564]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[565-version-negotiation]
ssl_conf = 565-version-negotiation-ssl

[565-version-negotiation-ssl]
server = 565-version-negotiation-server
client = 565-version-negotiation-client

[565-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[565-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-565]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[566-version-negotiation]
ssl_conf = 566-version-negotiation-ssl

[566-version-negotiation-ssl]
server = 566-version-negotiation-server
client = 566-version-negotiation-client

[566-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[566-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-566]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[567-version-negotiation]
ssl_conf = 567-version-negotiation-ssl

[567-version-negotiation-ssl]
server = 567-version-negotiation-server
client = 567-version-negotiation-client

[567-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[567-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-567]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[568-version-negotiation]
ssl_conf = 568-version-negotiation-ssl

[568-version-negotiation-ssl]
server = 568-version-negotiation-server
client = 568-version-negotiation-client

[568-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[568-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-568]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[569-version-negotiation]
ssl_conf = 569-version-negotiation-ssl

[569-version-negotiation-ssl]
server = 569-version-negotiation-server
client = 569-version-negotiation-client

[569-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[569-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-569]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[570-version-negotiation]
ssl_conf = 570-version-negotiation-ssl

[570-version-negotiation-ssl]
server = 570-version-negotiation-server
client = 570-version-negotiation-client

[570-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[570-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-570]
ExpectedResult = ServerFail


# ===========================================================

[571-version-negotiation]
ssl_conf = 571-version-negotiation-ssl

[571-version-negotiation-ssl]
server = 571-version-negotiation-server
client = 571-version-negotiation-client

[571-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[571-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-571]
ExpectedResult = ServerFail


# ===========================================================

[572-version-negotiation]
ssl_conf = 572-version-negotiation-ssl

[572-version-negotiation-ssl]
server = 572-version-negotiation-server
client = 572-version-negotiation-client

[572-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[572-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-572]
ExpectedResult = ServerFail


# ===========================================================

[573-version-negotiation]
ssl_conf = 573-version-negotiation-ssl

[573-version-negotiation-ssl]
server = 573-version-negotiation-server
client = 573-version-negotiation-client

[573-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[573-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-573]
ExpectedResult = ServerFail


# ===========================================================

[574-version-negotiation]
ssl_conf = 574-version-negotiation-ssl

[574-version-negotiation-ssl]
server = 574-version-negotiation-server
client = 574-version-negotiation-client

[574-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[574-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-574]
ExpectedResult = ServerFail


# ===========================================================

[575-version-negotiation]
ssl_conf = 575-version-negotiation-ssl

[575-version-negotiation-ssl]
server = 575-version-negotiation-server
client = 575-version-negotiation-client

[575-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[575-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-575]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[576-version-negotiation]
ssl_conf = 576-version-negotiation-ssl

[576-version-negotiation-ssl]
server = 576-version-negotiation-server
client = 576-version-negotiation-client

[576-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[576-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-576]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[577-version-negotiation]
ssl_conf = 577-version-negotiation-ssl

[577-version-negotiation-ssl]
server = 577-version-negotiation-server
client = 577-version-negotiation-client

[577-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[577-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-577]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[578-version-negotiation]
ssl_conf = 578-version-negotiation-ssl

[578-version-negotiation-ssl]
server = 578-version-negotiation-server
client = 578-version-negotiation-client

[578-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[578-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-578]
ExpectedResult = ServerFail


# ===========================================================

[579-version-negotiation]
ssl_conf = 579-version-negotiation-ssl

[579-version-negotiation-ssl]
server = 579-version-negotiation-server
client = 579-version-negotiation-client

[579-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[579-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-579]
ExpectedResult = ServerFail


# ===========================================================

[580-version-negotiation]
ssl_conf = 580-version-negotiation-ssl

[580-version-negotiation-ssl]
server = 580-version-negotiation-server
client = 580-version-negotiation-client

[580-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[580-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-580]
ExpectedResult = ServerFail


# ===========================================================

[581-version-negotiation]
ssl_conf = 581-version-negotiation-ssl

[581-version-negotiation-ssl]
server = 581-version-negotiation-server
client = 581-version-negotiation-client

[581-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[581-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-581]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[582-version-negotiation]
ssl_conf = 582-version-negotiation-ssl

[582-version-negotiation-ssl]
server = 582-version-negotiation-server
client = 582-version-negotiation-client

[582-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[582-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-582]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[583-version-negotiation]
ssl_conf = 583-version-negotiation-ssl

[583-version-negotiation-ssl]
server = 583-version-negotiation-server
client = 583-version-negotiation-client

[583-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[583-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-583]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[584-version-negotiation]
ssl_conf = 584-version-negotiation-ssl

[584-version-negotiation-ssl]
server = 584-version-negotiation-server
client = 584-version-negotiation-client

[584-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[584-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-584]
ExpectedResult = ServerFail


# ===========================================================

[585-version-negotiation]
ssl_conf = 585-version-negotiation-ssl

[585-version-negotiation-ssl]
server = 585-version-negotiation-server
client = 585-version-negotiation-client

[585-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[585-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-585]
ExpectedResult = ServerFail


# ===========================================================

[586-version-negotiation]
ssl_conf = 586-version-negotiation-ssl

[586-version-negotiation-ssl]
server = 586-version-negotiation-server
client = 586-version-negotiation-client

[586-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[586-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-586]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[587-version-negotiation]
ssl_conf = 587-version-negotiation-ssl

[587-version-negotiation-ssl]
server = 587-version-negotiation-server
client = 587-version-negotiation-client

[587-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[587-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-587]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[588-version-negotiation]
ssl_conf = 588-version-negotiation-ssl

[588-version-negotiation-ssl]
server = 588-version-negotiation-server
client = 588-version-negotiation-client

[588-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[588-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-588]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[589-version-negotiation]
ssl_conf = 589-version-negotiation-ssl

[589-version-negotiation-ssl]
server = 589-version-negotiation-server
client = 589-version-negotiation-client

[589-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[589-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-589]
ExpectedResult = ServerFail


# ===========================================================

[590-version-negotiation]
ssl_conf = 590-version-negotiation-ssl

[590-version-negotiation-ssl]
server = 590-version-negotiation-server
client = 590-version-negotiation-client

[590-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[590-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-590]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[591-version-negotiation]
ssl_conf = 591-version-negotiation-ssl

[591-version-negotiation-ssl]
server = 591-version-negotiation-server
client = 591-version-negotiation-client

[591-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[591-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-591]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[592-version-negotiation]
ssl_conf = 592-version-negotiation-ssl

[592-version-negotiation-ssl]
server = 592-version-negotiation-server
client = 592-version-negotiation-client

[592-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[592-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-592]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[593-version-negotiation]
ssl_conf = 593-version-negotiation-ssl

[593-version-negotiation-ssl]
server = 593-version-negotiation-server
client = 593-version-negotiation-client

[593-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[593-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-593]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[594-version-negotiation]
ssl_conf = 594-version-negotiation-ssl

[594-version-negotiation-ssl]
server = 594-version-negotiation-server
client = 594-version-negotiation-client

[594-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[594-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-594]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[595-version-negotiation]
ssl_conf = 595-version-negotiation-ssl

[595-version-negotiation-ssl]
server = 595-version-negotiation-server
client = 595-version-negotiation-client

[595-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[595-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-595]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[596-version-negotiation]
ssl_conf = 596-version-negotiation-ssl

[596-version-negotiation-ssl]
server = 596-version-negotiation-server
client = 596-version-negotiation-client

[596-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[596-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-596]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[597-version-negotiation]
ssl_conf = 597-version-negotiation-ssl

[597-version-negotiation-ssl]
server = 597-version-negotiation-server
client = 597-version-negotiation-client

[597-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[597-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-597]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[598-version-negotiation]
ssl_conf = 598-version-negotiation-ssl

[598-version-negotiation-ssl]
server = 598-version-negotiation-server
client = 598-version-negotiation-client

[598-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[598-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-598]
ExpectedResult = ServerFail


# ===========================================================

[599-version-negotiation]
ssl_conf = 599-version-negotiation-ssl

[599-version-negotiation-ssl]
server = 599-version-negotiation-server
client = 599-version-negotiation-client

[599-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[599-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-599]
ExpectedResult = ServerFail


# ===========================================================

[600-version-negotiation]
ssl_conf = 600-version-negotiation-ssl

[600-version-negotiation-ssl]
server = 600-version-negotiation-server
client = 600-version-negotiation-client

[600-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[600-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-600]
ExpectedResult = ServerFail


# ===========================================================

[601-version-negotiation]
ssl_conf = 601-version-negotiation-ssl

[601-version-negotiation-ssl]
server = 601-version-negotiation-server
client = 601-version-negotiation-client

[601-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[601-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-601]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[602-version-negotiation]
ssl_conf = 602-version-negotiation-ssl

[602-version-negotiation-ssl]
server = 602-version-negotiation-server
client = 602-version-negotiation-client

[602-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[602-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-602]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[603-version-negotiation]
ssl_conf = 603-version-negotiation-ssl

[603-version-negotiation-ssl]
server = 603-version-negotiation-server
client = 603-version-negotiation-client

[603-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[603-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-603]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[604-version-negotiation]
ssl_conf = 604-version-negotiation-ssl

[604-version-negotiation-ssl]
server = 604-version-negotiation-server
client = 604-version-negotiation-client

[604-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[604-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-604]
ExpectedResult = ServerFail


# ===========================================================

[605-version-negotiation]
ssl_conf = 605-version-negotiation-ssl

[605-version-negotiation-ssl]
server = 605-version-negotiation-server
client = 605-version-negotiation-client

[605-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[605-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-605]
ExpectedResult = ServerFail


# ===========================================================

[606-version-negotiation]
ssl_conf = 606-version-negotiation-ssl

[606-version-negotiation-ssl]
server = 606-version-negotiation-server
client = 606-version-negotiation-client

[606-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[606-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-606]
ExpectedResult = ServerFail


# ===========================================================

[607-version-negotiation]
ssl_conf = 607-version-negotiation-ssl

[607-version-negotiation-ssl]
server = 607-version-negotiation-server
client = 607-version-negotiation-client

[607-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[607-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-607]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[608-version-negotiation]
ssl_conf = 608-version-negotiation-ssl

[608-version-negotiation-ssl]
server = 608-version-negotiation-server
client = 608-version-negotiation-client

[608-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[608-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-608]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[609-version-negotiation]
ssl_conf = 609-version-negotiation-ssl

[609-version-negotiation-ssl]
server = 609-version-negotiation-server
client = 609-version-negotiation-client

[609-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[609-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-609]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[610-version-negotiation]
ssl_conf = 610-version-negotiation-ssl

[610-version-negotiation-ssl]
server = 610-version-negotiation-server
client = 610-version-negotiation-client

[610-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[610-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-610]
ExpectedResult = ServerFail


# ===========================================================

[611-version-negotiation]
ssl_conf = 611-version-negotiation-ssl

[611-version-negotiation-ssl]
server = 611-version-negotiation-server
client = 611-version-negotiation-client

[611-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[611-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-611]
ExpectedResult = ServerFail


# ===========================================================

[612-version-negotiation]
ssl_conf = 612-version-negotiation-ssl

[612-version-negotiation-ssl]
server = 612-version-negotiation-server
client = 612-version-negotiation-client

[612-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[612-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-612]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[613-version-negotiation]
ssl_conf = 613-version-negotiation-ssl

[613-version-negotiation-ssl]
server = 613-version-negotiation-server
client = 613-version-negotiation-client

[613-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[613-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-613]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[614-version-negotiation]
ssl_conf = 614-version-negotiation-ssl

[614-version-negotiation-ssl]
server = 614-version-negotiation-server
client = 614-version-negotiation-client

[614-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[614-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-614]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[615-version-negotiation]
ssl_conf = 615-version-negotiation-ssl

[615-version-negotiation-ssl]
server = 615-version-negotiation-server
client = 615-version-negotiation-client

[615-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[615-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-615]
ExpectedResult = ServerFail


# ===========================================================

[616-version-negotiation]
ssl_conf = 616-version-negotiation-ssl

[616-version-negotiation-ssl]
server = 616-version-negotiation-server
client = 616-version-negotiation-client

[616-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[616-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-616]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[617-version-negotiation]
ssl_conf = 617-version-negotiation-ssl

[617-version-negotiation-ssl]
server = 617-version-negotiation-server
client = 617-version-negotiation-client

[617-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[617-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-617]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[618-version-negotiation]
ssl_conf = 618-version-negotiation-ssl

[618-version-negotiation-ssl]
server = 618-version-negotiation-server
client = 618-version-negotiation-client

[618-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[618-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-618]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[619-version-negotiation]
ssl_conf = 619-version-negotiation-ssl

[619-version-negotiation-ssl]
server = 619-version-negotiation-server
client = 619-version-negotiation-client

[619-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[619-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-619]
ExpectedProtocol = TLSv1.2
ExpectedResult = Success


# ===========================================================

[620-version-negotiation]
ssl_conf = 620-version-negotiation-ssl

[620-version-negotiation-ssl]
server = 620-version-negotiation-server
client = 620-version-negotiation-client

[620-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[620-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-620]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[621-version-negotiation]
ssl_conf = 621-version-negotiation-ssl

[621-version-negotiation-ssl]
server = 621-version-negotiation-server
client = 621-version-negotiation-client

[621-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[621-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-621]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[622-version-negotiation]
ssl_conf = 622-version-negotiation-ssl

[622-version-negotiation-ssl]
server = 622-version-negotiation-server
client = 622-version-negotiation-client

[622-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[622-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-622]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[623-version-negotiation]
ssl_conf = 623-version-negotiation-ssl

[623-version-negotiation-ssl]
server = 623-version-negotiation-server
client = 623-version-negotiation-client

[623-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[623-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-623]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[624-version-negotiation]
ssl_conf = 624-version-negotiation-ssl

[624-version-negotiation-ssl]
server = 624-version-negotiation-server
client = 624-version-negotiation-client

[624-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[624-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-624]
ExpectedResult = ServerFail


# ===========================================================

[625-version-negotiation]
ssl_conf = 625-version-negotiation-ssl

[625-version-negotiation-ssl]
server = 625-version-negotiation-server
client = 625-version-negotiation-client

[625-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[625-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-625]
ExpectedResult = ServerFail


# ===========================================================

[626-version-negotiation]
ssl_conf = 626-version-negotiation-ssl

[626-version-negotiation-ssl]
server = 626-version-negotiation-server
client = 626-version-negotiation-client

[626-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[626-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-626]
ExpectedResult = ServerFail


# ===========================================================

[627-version-negotiation]
ssl_conf = 627-version-negotiation-ssl

[627-version-negotiation-ssl]
server = 627-version-negotiation-server
client = 627-version-negotiation-client

[627-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[627-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-627]
ExpectedResult = ServerFail


# ===========================================================

[628-version-negotiation]
ssl_conf = 628-version-negotiation-ssl

[628-version-negotiation-ssl]
server = 628-version-negotiation-server
client = 628-version-negotiation-client

[628-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[628-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-628]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[629-version-negotiation]
ssl_conf = 629-version-negotiation-ssl

[629-version-negotiation-ssl]
server = 629-version-negotiation-server
client = 629-version-negotiation-client

[629-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[629-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-629]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[630-version-negotiation]
ssl_conf = 630-version-negotiation-ssl

[630-version-negotiation-ssl]
server = 630-version-negotiation-server
client = 630-version-negotiation-client

[630-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[630-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-630]
ExpectedResult = ServerFail


# ===========================================================

[631-version-negotiation]
ssl_conf = 631-version-negotiation-ssl

[631-version-negotiation-ssl]
server = 631-version-negotiation-server
client = 631-version-negotiation-client

[631-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[631-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-631]
ExpectedResult = ServerFail


# ===========================================================

[632-version-negotiation]
ssl_conf = 632-version-negotiation-ssl

[632-version-negotiation-ssl]
server = 632-version-negotiation-server
client = 632-version-negotiation-client

[632-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[632-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-632]
ExpectedResult = ServerFail


# ===========================================================

[633-version-negotiation]
ssl_conf = 633-version-negotiation-ssl

[633-version-negotiation-ssl]
server = 633-version-negotiation-server
client = 633-version-negotiation-client

[633-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[633-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-633]
ExpectedResult = ServerFail


# ===========================================================

[634-version-negotiation]
ssl_conf = 634-version-negotiation-ssl

[634-version-negotiation-ssl]
server = 634-version-negotiation-server
client = 634-version-negotiation-client

[634-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[634-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-634]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[635-version-negotiation]
ssl_conf = 635-version-negotiation-ssl

[635-version-negotiation-ssl]
server = 635-version-negotiation-server
client = 635-version-negotiation-client

[635-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[635-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-635]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[636-version-negotiation]
ssl_conf = 636-version-negotiation-ssl

[636-version-negotiation-ssl]
server = 636-version-negotiation-server
client = 636-version-negotiation-client

[636-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[636-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-636]
ExpectedResult = ServerFail


# ===========================================================

[637-version-negotiation]
ssl_conf = 637-version-negotiation-ssl

[637-version-negotiation-ssl]
server = 637-version-negotiation-server
client = 637-version-negotiation-client

[637-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[637-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-637]
ExpectedResult = ServerFail


# ===========================================================

[638-version-negotiation]
ssl_conf = 638-version-negotiation-ssl

[638-version-negotiation-ssl]
server = 638-version-negotiation-server
client = 638-version-negotiation-client

[638-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[638-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-638]
ExpectedResult = ServerFail


# ===========================================================

[639-version-negotiation]
ssl_conf = 639-version-negotiation-ssl

[639-version-negotiation-ssl]
server = 639-version-negotiation-server
client = 639-version-negotiation-client

[639-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[639-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-639]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[640-version-negotiation]
ssl_conf = 640-version-negotiation-ssl

[640-version-negotiation-ssl]
server = 640-version-negotiation-server
client = 640-version-negotiation-client

[640-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[640-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-640]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[641-version-negotiation]
ssl_conf = 641-version-negotiation-ssl

[641-version-negotiation-ssl]
server = 641-version-negotiation-server
client = 641-version-negotiation-client

[641-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[641-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-641]
ExpectedResult = ServerFail


# ===========================================================

[642-version-negotiation]
ssl_conf = 642-version-negotiation-ssl

[642-version-negotiation-ssl]
server = 642-version-negotiation-server
client = 642-version-negotiation-client

[642-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[642-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-642]
ExpectedResult = ServerFail


# ===========================================================

[643-version-negotiation]
ssl_conf = 643-version-negotiation-ssl

[643-version-negotiation-ssl]
server = 643-version-negotiation-server
client = 643-version-negotiation-client

[643-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[643-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-643]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[644-version-negotiation]
ssl_conf = 644-version-negotiation-ssl

[644-version-negotiation-ssl]
server = 644-version-negotiation-server
client = 644-version-negotiation-client

[644-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[644-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-644]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[645-version-negotiation]
ssl_conf = 645-version-negotiation-ssl

[645-version-negotiation-ssl]
server = 645-version-negotiation-server
client = 645-version-negotiation-client

[645-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[645-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-645]
ExpectedResult = ServerFail


# ===========================================================

[646-version-negotiation]
ssl_conf = 646-version-negotiation-ssl

[646-version-negotiation-ssl]
server = 646-version-negotiation-server
client = 646-version-negotiation-client

[646-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[646-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-646]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[647-version-negotiation]
ssl_conf = 647-version-negotiation-ssl

[647-version-negotiation-ssl]
server = 647-version-negotiation-server
client = 647-version-negotiation-client

[647-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[647-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-647]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[648-version-negotiation]
ssl_conf = 648-version-negotiation-ssl

[648-version-negotiation-ssl]
server = 648-version-negotiation-server
client = 648-version-negotiation-client

[648-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[648-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-648]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[649-version-negotiation]
ssl_conf = 649-version-negotiation-ssl

[649-version-negotiation-ssl]
server = 649-version-negotiation-server
client = 649-version-negotiation-client

[649-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[649-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-649]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[650-version-negotiation]
ssl_conf = 650-version-negotiation-ssl

[650-version-negotiation-ssl]
server = 650-version-negotiation-server
client = 650-version-negotiation-client

[650-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[650-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-650]
ExpectedResult = ServerFail


# ===========================================================

[651-version-negotiation]
ssl_conf = 651-version-negotiation-ssl

[651-version-negotiation-ssl]
server = 651-version-negotiation-server
client = 651-version-negotiation-client

[651-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[651-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-651]
ExpectedResult = ServerFail


# ===========================================================

[652-version-negotiation]
ssl_conf = 652-version-negotiation-ssl

[652-version-negotiation-ssl]
server = 652-version-negotiation-server
client = 652-version-negotiation-client

[652-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[652-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-652]
ExpectedResult = ServerFail


# ===========================================================

[653-version-negotiation]
ssl_conf = 653-version-negotiation-ssl

[653-version-negotiation-ssl]
server = 653-version-negotiation-server
client = 653-version-negotiation-client

[653-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[653-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-653]
ExpectedResult = ServerFail


# ===========================================================

[654-version-negotiation]
ssl_conf = 654-version-negotiation-ssl

[654-version-negotiation-ssl]
server = 654-version-negotiation-server
client = 654-version-negotiation-client

[654-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[654-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-654]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[655-version-negotiation]
ssl_conf = 655-version-negotiation-ssl

[655-version-negotiation-ssl]
server = 655-version-negotiation-server
client = 655-version-negotiation-client

[655-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[655-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-655]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[656-version-negotiation]
ssl_conf = 656-version-negotiation-ssl

[656-version-negotiation-ssl]
server = 656-version-negotiation-server
client = 656-version-negotiation-client

[656-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = SSLv3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[656-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-656]
ExpectedResult = ServerFail


# ===========================================================

[657-version-negotiation]
ssl_conf = 657-version-negotiation-ssl

[657-version-negotiation-ssl]
server = 657-version-negotiation-server
client = 657-version-negotiation-client

[657-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[657-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-657]
ExpectedResult = ServerFail


# ===========================================================

[658-version-negotiation]
ssl_conf = 658-version-negotiation-ssl

[658-version-negotiation-ssl]
server = 658-version-negotiation-server
client = 658-version-negotiation-client

[658-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[658-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-658]
ExpectedResult = ServerFail


# ===========================================================

[659-version-negotiation]
ssl_conf = 659-version-negotiation-ssl

[659-version-negotiation-ssl]
server = 659-version-negotiation-server
client = 659-version-negotiation-client

[659-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[659-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-659]
ExpectedResult = ServerFail


# ===========================================================

[660-version-negotiation]
ssl_conf = 660-version-negotiation-ssl

[660-version-negotiation-ssl]
server = 660-version-negotiation-server
client = 660-version-negotiation-client

[660-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[660-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-660]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[661-version-negotiation]
ssl_conf = 661-version-negotiation-ssl

[661-version-negotiation-ssl]
server = 661-version-negotiation-server
client = 661-version-negotiation-client

[661-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = SSLv3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[661-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-661]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[662-version-negotiation]
ssl_conf = 662-version-negotiation-ssl

[662-version-negotiation-ssl]
server = 662-version-negotiation-server
client = 662-version-negotiation-client

[662-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[662-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-662]
ExpectedResult = ServerFail


# ===========================================================

[663-version-negotiation]
ssl_conf = 663-version-negotiation-ssl

[663-version-negotiation-ssl]
server = 663-version-negotiation-server
client = 663-version-negotiation-client

[663-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[663-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-663]
ExpectedResult = ServerFail


# ===========================================================

[664-version-negotiation]
ssl_conf = 664-version-negotiation-ssl

[664-version-negotiation-ssl]
server = 664-version-negotiation-server
client = 664-version-negotiation-client

[664-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[664-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-664]
ExpectedResult = ServerFail


# ===========================================================

[665-version-negotiation]
ssl_conf = 665-version-negotiation-ssl

[665-version-negotiation-ssl]
server = 665-version-negotiation-server
client = 665-version-negotiation-client

[665-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[665-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-665]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[666-version-negotiation]
ssl_conf = 666-version-negotiation-ssl

[666-version-negotiation-ssl]
server = 666-version-negotiation-server
client = 666-version-negotiation-client

[666-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[666-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-666]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[667-version-negotiation]
ssl_conf = 667-version-negotiation-ssl

[667-version-negotiation-ssl]
server = 667-version-negotiation-server
client = 667-version-negotiation-client

[667-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.1
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[667-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-667]
ExpectedResult = ServerFail


# ===========================================================

[668-version-negotiation]
ssl_conf = 668-version-negotiation-ssl

[668-version-negotiation-ssl]
server = 668-version-negotiation-server
client = 668-version-negotiation-client

[668-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[668-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-668]
ExpectedResult = ServerFail


# ===========================================================

[669-version-negotiation]
ssl_conf = 669-version-negotiation-ssl

[669-version-negotiation-ssl]
server = 669-version-negotiation-server
client = 669-version-negotiation-client

[669-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[669-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-669]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[670-version-negotiation]
ssl_conf = 670-version-negotiation-ssl

[670-version-negotiation-ssl]
server = 670-version-negotiation-server
client = 670-version-negotiation-client

[670-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.1
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[670-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-670]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[671-version-negotiation]
ssl_conf = 671-version-negotiation-ssl

[671-version-negotiation-ssl]
server = 671-version-negotiation-server
client = 671-version-negotiation-client

[671-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.2
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[671-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-671]
ExpectedResult = ServerFail


# ===========================================================

[672-version-negotiation]
ssl_conf = 672-version-negotiation-ssl

[672-version-negotiation-ssl]
server = 672-version-negotiation-server
client = 672-version-negotiation-client

[672-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[672-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-672]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[673-version-negotiation]
ssl_conf = 673-version-negotiation-ssl

[673-version-negotiation-ssl]
server = 673-version-negotiation-server
client = 673-version-negotiation-client

[673-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[673-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-673]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[674-version-negotiation]
ssl_conf = 674-version-negotiation-ssl

[674-version-negotiation-ssl]
server = 674-version-negotiation-server
client = 674-version-negotiation-client

[674-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[674-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-674]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[675-version-negotiation]
ssl_conf = 675-version-negotiation-ssl

[675-version-negotiation-ssl]
server = 675-version-negotiation-server
client = 675-version-negotiation-client

[675-version-negotiation-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[675-version-negotiation-client]
CipherString = DEFAULT:@SECLEVEL=0
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-675]
ExpectedProtocol = TLSv1.3
ExpectedResult = Success


# ===========================================================

[676-ciphersuite-sanity-check-client]
ssl_conf = 676-ciphersuite-sanity-check-client-ssl

[676-ciphersuite-sanity-check-client-ssl]
server = 676-ciphersuite-sanity-check-client-server
client = 676-ciphersuite-sanity-check-client-client

[676-ciphersuite-sanity-check-client-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = DEFAULT
MaxProtocol = TLSv1.2
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[676-ciphersuite-sanity-check-client-client]
CipherString = AES128-SHA
Ciphersuites = 
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-676]
ExpectedResult = ClientFail


# ===========================================================

[677-ciphersuite-sanity-check-server]
ssl_conf = 677-ciphersuite-sanity-check-server-ssl

[677-ciphersuite-sanity-check-server-ssl]
server = 677-ciphersuite-sanity-check-server-server
client = 677-ciphersuite-sanity-check-server-client

[677-ciphersuite-sanity-check-server-server]
Certificate = ${ENV::TEST_CERTS_DIR}/servercert.pem
CipherString = AES128-SHA
Ciphersuites = 
PrivateKey = ${ENV::TEST_CERTS_DIR}/serverkey.pem

[677-ciphersuite-sanity-check-server-client]
CipherString = AES128-SHA
MaxProtocol = TLSv1.2
VerifyCAFile = ${ENV::TEST_CERTS_DIR}/rootcert.pem
VerifyMode = Peer

[test-677]
ExpectedResult = ServerFail


