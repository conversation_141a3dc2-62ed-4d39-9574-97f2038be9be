# Generated with generate_ssl_tests.pl

num_tests = 3

test-0 = 0-test client can verify server by delegated credential
test-1 = 1-test client/server can do mtls by delegated credential
test-2 = 2-test depoly cert and dc at the same time, but do not open dc 
# ===========================================================

[0-test client can verify server by delegated credential]
ssl_conf = 0-test client can verify server by delegated credential-ssl

[0-test client can verify server by delegated credential-ssl]
server = 0-test client can verify server by delegated credential-server
client = 0-test client can verify server by delegated credential-client

[0-test client can verify server by delegated credential-server]
Certificate = ${ENV::TEST_RUNS_DIR}/dc/dc-leaf-server.crt
CipherString = DEFAULT
DC = ${ENV::TEST_RUNS_DIR}/dc/dc-server.dc
DCKey = ${ENV::TEST_RUNS_DIR}/dc/dc-server.key
Enable_sign_by_dc = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_RUNS_DIR}/dc/dc-leaf-server.key

[0-test client can verify server by delegated credential-client]
CipherString = DEFAULT
Enable_verify_peer_by_dc = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
VerifyCAFile = ${ENV::TEST_RUNS_DIR}/dc/dc-chain-ca.crt
VerifyMode = Peer

[test-0]
ExpectedResult = Success
ExpextClientDCusage = VerifyPeerByDCOnly
ExpextServerDCusage = SignByDCOnly


# ===========================================================

[1-test client/server can do mtls by delegated credential]
ssl_conf = 1-test client/server can do mtls by delegated credential-ssl

[1-test client/server can do mtls by delegated credential-ssl]
server = 1-test client/server can do mtls by delegated credential-server
client = 1-test client/server can do mtls by delegated credential-client

[1-test client/server can do mtls by delegated credential-server]
Certificate = ${ENV::TEST_RUNS_DIR}/dc/dc-leaf-server.crt
CipherString = DEFAULT
DC = ${ENV::TEST_RUNS_DIR}/dc/dc-server.dc
DCKey = ${ENV::TEST_RUNS_DIR}/dc/dc-server.key
Enable_sign_by_dc = on
Enable_verify_peer_by_dc = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_RUNS_DIR}/dc/dc-leaf-server.key
VerifyCAFile = ${ENV::TEST_RUNS_DIR}/dc/dc-chain-ca.crt
VerifyMode = Request

[1-test client/server can do mtls by delegated credential-client]
Certificate = ${ENV::TEST_RUNS_DIR}/dc/dc-leaf-client.crt
CipherString = DEFAULT
DC = ${ENV::TEST_RUNS_DIR}/dc/dc-client.dc
DCKey = ${ENV::TEST_RUNS_DIR}/dc/dc-client.key
Enable_sign_by_dc = on
Enable_verify_peer_by_dc = on
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_RUNS_DIR}/dc/dc-leaf-client.key
VerifyCAFile = ${ENV::TEST_RUNS_DIR}/dc/dc-chain-ca.crt
VerifyMode = Peer

[test-1]
ExpectedResult = Success
ExpextClientDCusage = VerifyPeerAndSignByDC
ExpextServerDCusage = VerifyPeerAndSignByDC


# ===========================================================

[2-test depoly cert and dc at the same time, but do not open dc ]
ssl_conf = 2-test depoly cert and dc at the same time, but do not open dc -ssl

[2-test depoly cert and dc at the same time, but do not open dc -ssl]
server = 2-test depoly cert and dc at the same time, but do not open dc -server
client = 2-test depoly cert and dc at the same time, but do not open dc -client

[2-test depoly cert and dc at the same time, but do not open dc -server]
Certificate = ${ENV::TEST_RUNS_DIR}/dc/dc-leaf-server.crt
CipherString = DEFAULT
DC = ${ENV::TEST_RUNS_DIR}/dc/dc-server.dc
DCKey = ${ENV::TEST_RUNS_DIR}/dc/dc-server.key
Enable_sign_by_dc = off
Enable_verify_peer_by_dc = off
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_RUNS_DIR}/dc/dc-leaf-server.key
VerifyCAFile = ${ENV::TEST_RUNS_DIR}/dc/dc-chain-ca.crt
VerifyMode = Request

[2-test depoly cert and dc at the same time, but do not open dc -client]
Certificate = ${ENV::TEST_RUNS_DIR}/dc/dc-leaf-client.crt
CipherString = DEFAULT
DC = ${ENV::TEST_RUNS_DIR}/dc/dc-client.dc
DCKey = ${ENV::TEST_RUNS_DIR}/dc/dc-client.key
Enable_sign_by_dc = off
Enable_verify_peer_by_dc = off
MaxProtocol = TLSv1.3
MinProtocol = TLSv1.3
PrivateKey = ${ENV::TEST_RUNS_DIR}/dc/dc-leaf-client.key
VerifyCAFile = ${ENV::TEST_RUNS_DIR}/dc/dc-chain-ca.crt
VerifyMode = Peer

[test-2]
ExpectedResult = Success
ExpextClientDCusage = NotUseDC
ExpextServerDCusage = NotUseDC


