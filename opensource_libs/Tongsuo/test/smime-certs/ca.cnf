#
# OpenSSL example configuration file for automated certificate creation.
#

# Comment out the next line to ignore configuration errors
config_diagnostics = 1

# This definition stops the following lines choking if HOME or CN
# is undefined.
HOME			= .
CN			= "Not Defined"
default_ca		= ca

####################################################################
[ req ]
default_bits		= 2048
default_keyfile 	= privkey.pem
# Don't prompt for fields: use those in section directly
prompt			= no
distinguished_name	= req_distinguished_name
x509_extensions	= v3_ca	# The extensions to add to the self signed cert
string_mask = utf8only

# req_extensions = v3_req # The extensions to add to a certificate request

[ req_distinguished_name ]
countryName			= UK

organizationName		= OpenSSL Group
# Take CN from environment so it can come from a script.
commonName			= $ENV::CN

[ usr_cert ]

# These extensions are added when 'ca' signs a request for an end entity
# certificate

basicConstraints=critical, CA:FALSE
keyUsage=critical, nonRepudiation, digitalSignature, keyEncipherment

# PKIX recommendations harmless if included in all certificates.
subjectKeyIdentifier=hash
authorityKeyIdentifier=keyid

[ dh_cert ]

# These extensions are added when 'ca' signs a request for an end entity
# DH certificate

basicConstraints=critical, CA:FALSE
keyUsage=critical, keyAgreement

# PKIX recommendations harmless if included in all certificates.
subjectKeyIdentifier=hash
authorityKeyIdentifier=keyid

[ v3_ca ]


# Extensions for a typical CA

# PKIX recommendation.

subjectKeyIdentifier=hash
authorityKeyIdentifier=keyid:always
basicConstraints = critical,CA:true
keyUsage = critical, cRLSign, keyCertSign

