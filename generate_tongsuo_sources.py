#!/usr/bin/env python3
"""
Script to generate complete Tongsuo source file list from build.info files
"""

import os
import re
import glob

def parse_build_info(file_path):
    """Parse a build.info file and extract source files"""
    sources = []

    try:
        with open(file_path, 'r') as f:
            content = f.read()

        # Find SOURCE[...] lines that target libcrypto
        source_pattern = r'SOURCE\[([^\]]*libcrypto[^\]]*)\]\s*=\s*([^#\n]+(?:\\\s*\n[^#\n]*)*)'
        matches = re.findall(source_pattern, content, re.MULTILINE)

        for target, match in matches:
            # Clean up the match - remove backslashes and newlines
            cleaned = re.sub(r'\\\s*\n\s*', ' ', match)
            # Split by whitespace and filter out empty strings
            files = [f.strip() for f in cleaned.split() if f.strip()]
            sources.extend(files)

        # Also look for $COMMON variable definitions
        common_pattern = r'\$COMMON\s*=\s*([^#\n]+(?:\\\s*\n[^#\n]*)*)'
        common_matches = re.findall(common_pattern, content, re.MULTILINE)

        for match in common_matches:
            cleaned = re.sub(r'\\\s*\n\s*', ' ', match)
            files = [f.strip() for f in cleaned.split() if f.strip()]
            sources.extend(files)

    except Exception as e:
        print(f"Error parsing {file_path}: {e}")

    return sources

def find_all_build_info_files(root_dir):
    """Find all build.info files in the directory tree"""
    build_info_files = []
    
    for root, dirs, files in os.walk(root_dir):
        if 'build.info' in files:
            build_info_files.append(os.path.join(root, 'build.info'))
    
    return build_info_files

def get_crypto_sources():
    """Get all crypto sources from Tongsuo"""
    tongsuo_dir = 'opensource_libs/Tongsuo'
    crypto_dir = os.path.join(tongsuo_dir, 'crypto')
    
    all_sources = set()
    
    # Find all build.info files in crypto directory
    build_info_files = find_all_build_info_files(crypto_dir)
    
    for build_info_file in build_info_files:
        print(f"Processing: {build_info_file}")
        sources = parse_build_info(build_info_file)
        
        # Convert relative paths to absolute paths from crypto directory
        rel_dir = os.path.relpath(os.path.dirname(build_info_file), crypto_dir)
        
        for source in sources:
            # Skip non-C files and special cases
            if not source.endswith('.c'):
                continue
            if source.startswith('../'):
                continue
            if source.startswith('$'):
                continue
                
            # Build the full path relative to crypto directory
            if rel_dir == '.':
                full_path = source
            else:
                full_path = os.path.join(rel_dir, source).replace('\\', '/')
            
            all_sources.add(full_path)
    
    # Add core crypto files that are always needed
    core_files = [
        'cryptlib.c', 'mem.c', 'mem_clr.c', 'ex_data.c', 'ctype.c',
        'threads_none.c', 'o_init.c', 'init.c', 'cpt_err.c', 'cpuid.c',
        'o_str.c', 'o_time.c', 'o_dir.c', 'o_fopen.c', 'getenv.c',
        'trace.c', 'provider.c', 'provider_core.c', 'provider_conf.c',
        'core_fetch.c', 'core_algorithm.c', 'core_namemap.c',
        'params.c', 'params_from_text.c', 'bsearch.c', 'context.c',
        'sparse_array.c', 'asn1_dsa.c', 'packet.c', 'param_build.c',
        'param_build_set.c', 'der_writer.c', 'threads_lib.c', 'params_dup.c',
        'cversion.c', 'info.c', 'ebcdic.c', 'uid.c', 'punycode.c',
        'passphrase.c', 'initthread.c', 'provider_child.c', 'provider_predefined.c',
        'self_test_core.c'
    ]
    
    for core_file in core_files:
        all_sources.add(core_file)
    
    return sorted(list(all_sources))

def generate_sources_mk():
    """Generate the sources.mk file"""
    sources = get_crypto_sources()

    print(f"Found {len(sources)} total sources")

    # Filter out sources that don't exist
    existing_sources = []
    tongsuo_crypto_dir = 'opensource_libs/Tongsuo/crypto'

    for source in sources:
        full_path = os.path.join(tongsuo_crypto_dir, source)
        if os.path.exists(full_path):
            existing_sources.append(source)
        else:
            print(f"Warning: Source file not found: {full_path}")

    print(f"Found {len(existing_sources)} existing sources")

    # Generate the makefile content
    content = """# Copyright (c) 2024, Tongsuo Project.
#
# Licensed under the Apache License, Version 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://github.com/Tongsuo-Project/Tongsuo/blob/master/LICENSE.txt

# This file is auto-generated for Trusty integration. Do not edit manually.

crypto_sources := \\
"""

    # Add sources with proper formatting
    for i, source in enumerate(existing_sources):
        if i == len(existing_sources) - 1:
            content += f"  crypto/{source}\n"
        else:
            content += f"  crypto/{source}\\\n"

    content += "\ncrypto_sources_asm :=\n\n"

    return content

if __name__ == '__main__':
    print("Generating complete Tongsuo sources...")
    content = generate_sources_mk()
    
    with open('opensource_libs/Tongsuo/sources.mk', 'w') as f:
        f.write(content)
    
    print("Generated sources.mk with complete source file list")
    lines_with_crypto = [l for l in content.split('\n') if 'crypto/' in l]
    print(f"Total source files: {len(lines_with_crypto)}")
